import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { fabric } from 'fabric/dist/fabric.min.js';
import { EleMessage } from 'ele-admin-plus/es';

/**
 * Fabric.js画布管理Composable
 * <AUTHOR>
 * @date 2025-01-15
 */
export function useFabricCanvas() {
  // 响应式数据
  const canvas = ref(null);
  const canvasContainer = ref(null);
  const isCanvasReady = ref(false);
  const selectedObjects = ref([]);
  const canvasHistory = ref([]);
  const historyIndex = ref(-1);
  const maxHistorySize = 50;

  // 画布配置
  const canvasConfig = ref({
    width: 576,  // 192mm * 3 (缩放比例)
    height: 78,  // 26mm * 3
    backgroundColor: '#ffffff',
    selectionColor: 'rgba(100, 100, 255, 0.3)',
    selectionLineWidth: 2,
    hoverCursor: 'move',
    moveCursor: 'move'
  });

  // 网格配置
  const gridConfig = ref({
    enabled: true,
    size: 10,
    color: '#e0e0e0',
    opacity: 0.5
  });

  /**
   * 初始化画布
   * @param {string} canvasId - 画布元素ID
   * @param {Object} options - 初始化选项
   */
  const initCanvas = async (canvasId, options = {}) => {
    try {
      await nextTick();

      const canvasElement = document.getElementById(canvasId);
      if (!canvasElement) {
        throw new Error(`找不到画布元素: ${canvasId}`);
      }

      // 合并配置
      const config = { ...canvasConfig.value, ...options };

      // 创建Fabric画布
      canvas.value = new fabric.Canvas(canvasId, config);

      // 设置画布容器引用
      canvasContainer.value = canvasElement.parentElement;

      // 初始化网格
      if (gridConfig.value.enabled) {
        drawGrid();
      }

      // 绑定事件监听器
      bindCanvasEvents();

      // 保存初始状态
      saveCanvasState();

      isCanvasReady.value = true;

      console.log('Fabric画布初始化成功', {
        width: config.width,
        height: config.height
      });

    } catch (error) {
      console.error('Fabric画布初始化失败:', error);
      EleMessage.error('画布初始化失败: ' + error.message);
      throw error;
    }
  };

  /**
   * 绘制网格
   */
  const drawGrid = () => {
    if (!canvas.value || !gridConfig.value.enabled) return;

    const { width, height } = canvasConfig.value;
    const { size, color, opacity } = gridConfig.value;

    // 清除现有网格
    const existingGrid = canvas.value.getObjects().filter(obj => obj.isGrid);
    existingGrid.forEach(obj => canvas.value.remove(obj));

    const gridGroup = [];

    // 绘制垂直线
    for (let i = 0; i <= width; i += size) {
      const line = new fabric.Line([i, 0, i, height], {
        stroke: color,
        strokeWidth: 1,
        opacity: opacity,
        selectable: false,
        evented: false,
        isGrid: true
      });
      gridGroup.push(line);
    }

    // 绘制水平线
    for (let i = 0; i <= height; i += size) {
      const line = new fabric.Line([0, i, width, i], {
        stroke: color,
        strokeWidth: 1,
        opacity: opacity,
        selectable: false,
        evented: false,
        isGrid: true
      });
      gridGroup.push(line);
    }

    // 添加网格到画布
    gridGroup.forEach(line => {
      canvas.value.add(line);
      canvas.value.sendToBack(line);
    });

    canvas.value.renderAll();
  };

  /**
   * 绑定画布事件
   */
  const bindCanvasEvents = () => {
    if (!canvas.value) return;

    // 选择对象事件
    canvas.value.on('selection:created', (e) => {
      selectedObjects.value = e.selected || [];
    });

    canvas.value.on('selection:updated', (e) => {
      selectedObjects.value = e.selected || [];
    });

    canvas.value.on('selection:cleared', () => {
      selectedObjects.value = [];
    });

    // 对象修改事件
    canvas.value.on('object:modified', () => {
      saveCanvasState();
    });

    canvas.value.on('object:added', () => {
      saveCanvasState();
    });

    canvas.value.on('object:removed', () => {
      saveCanvasState();
    });

    // 鼠标事件
    canvas.value.on('mouse:down', (e) => {
      if (!e.target) {
        // 点击空白区域，清除选择
        canvas.value.discardActiveObject();
        canvas.value.renderAll();
      }
    });
  };

  /**
   * 保存画布状态（用于撤销/重做）
   */
  const saveCanvasState = () => {
    if (!canvas.value) return;

    const state = JSON.stringify(canvas.value.toJSON(['id', 'elementType', 'dataBinding']));

    // 移除当前索引之后的历史记录
    canvasHistory.value = canvasHistory.value.slice(0, historyIndex.value + 1);

    // 添加新状态
    canvasHistory.value.push(state);

    // 限制历史记录大小
    if (canvasHistory.value.length > maxHistorySize) {
      canvasHistory.value.shift();
    } else {
      historyIndex.value++;
    }
  };

  /**
   * 撤销操作
   */
  const undo = () => {
    if (!canvas.value || historyIndex.value <= 0) return;

    historyIndex.value--;
    const state = canvasHistory.value[historyIndex.value];

    canvas.value.loadFromJSON(state, () => {
      canvas.value.renderAll();
      if (gridConfig.value.enabled) {
        drawGrid();
      }
    });
  };

  /**
   * 重做操作
   */
  const redo = () => {
    if (!canvas.value || historyIndex.value >= canvasHistory.value.length - 1) return;

    historyIndex.value++;
    const state = canvasHistory.value[historyIndex.value];

    canvas.value.loadFromJSON(state, () => {
      canvas.value.renderAll();
      if (gridConfig.value.enabled) {
        drawGrid();
      }
    });
  };

  /**
   * 清空画布
   */
  const clearCanvas = () => {
    if (!canvas.value) return;

    canvas.value.clear();
    if (gridConfig.value.enabled) {
      drawGrid();
    }
    saveCanvasState();
  };

  /**
   * 删除选中对象
   */
  const deleteSelected = () => {
    if (!canvas.value) return;

    const activeObjects = canvas.value.getActiveObjects();
    if (activeObjects.length > 0) {
      activeObjects.forEach(obj => {
        if (!obj.isGrid) {
          canvas.value.remove(obj);
        }
      });
      canvas.value.discardActiveObject();
      canvas.value.renderAll();
    }
  };

  /**
   * 复制选中对象
   */
  const copySelected = () => {
    if (!canvas.value) return Promise.resolve();

    return new Promise((resolve) => {
      const activeObject = canvas.value.getActiveObject();
      if (activeObject) {
        activeObject.clone((cloned) => {
          resolve(cloned);
        });
      } else {
        resolve(null);
      }
    });
  };

  /**
   * 粘贴对象
   * @param {Object} clonedObject - 克隆的对象
   */
  const pasteObject = (clonedObject) => {
    if (!canvas.value || !clonedObject) return;

    clonedObject.set({
      left: clonedObject.left + 10,
      top: clonedObject.top + 10
    });

    canvas.value.add(clonedObject);
    canvas.value.setActiveObject(clonedObject);
    canvas.value.renderAll();
  };

  /**
   * 设置网格显示状态
   * @param {boolean} enabled - 是否显示网格
   */
  const setGridEnabled = (enabled) => {
    gridConfig.value.enabled = enabled;
    if (enabled) {
      drawGrid();
    } else {
      // 移除网格
      const gridObjects = canvas.value.getObjects().filter(obj => obj.isGrid);
      gridObjects.forEach(obj => canvas.value.remove(obj));
      canvas.value.renderAll();
    }
  };

  /**
   * 获取画布JSON数据
   */
  const getCanvasJSON = () => {
    if (!canvas.value) return null;
    return canvas.value.toJSON(['id', 'elementType', 'dataBinding']);
  };

  /**
   * 从JSON加载画布
   * @param {Object} jsonData - JSON数据
   */
  const loadFromJSON = (jsonData) => {
    if (!canvas.value || !jsonData) return;

    canvas.value.loadFromJSON(jsonData, () => {
      canvas.value.renderAll();
      if (gridConfig.value.enabled) {
        drawGrid();
      }
      saveCanvasState();
    });
  };

  /**
   * 销毁画布
   */
  const destroyCanvas = () => {
    if (canvas.value) {
      canvas.value.dispose();
      canvas.value = null;
    }
    isCanvasReady.value = false;
    selectedObjects.value = [];
    canvasHistory.value = [];
    historyIndex.value = -1;
  };

  // 组件卸载时清理
  onUnmounted(() => {
    destroyCanvas();
  });

  return {
    // 响应式数据
    canvas,
    canvasContainer,
    isCanvasReady,
    selectedObjects,
    canvasConfig,
    gridConfig,

    // 方法
    initCanvas,
    drawGrid,
    saveCanvasState,
    undo,
    redo,
    clearCanvas,
    deleteSelected,
    copySelected,
    pasteObject,
    setGridEnabled,
    getCanvasJSON,
    loadFromJSON,
    destroyCanvas
  };
}
