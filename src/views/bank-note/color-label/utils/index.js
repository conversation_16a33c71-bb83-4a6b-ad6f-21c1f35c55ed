/**
 * 彩签相关工具函数
 * <AUTHOR>
 * @date 2025-01-15
 */

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateId() {
  return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * 坐标转换：设计坐标 → 实际坐标
 * @param {Object} designPos 设计坐标 {x, y}
 * @param {Object} canvasSize 画布尺寸 {width, height}
 * @param {Object} labelSize 标签尺寸 {width, height}
 * @returns {Object} 实际坐标 {x, y}
 */
export function convertCoordinates(designPos, canvasSize, labelSize) {
  return {
    x: (designPos.x / canvasSize.width) * labelSize.width,
    y: (designPos.y / canvasSize.height) * labelSize.height
  };
}

/**
 * 处理数据绑定模板
 * @param {string} template 模板字符串
 * @param {Object} data 数据对象
 * @returns {string} 处理后的字符串
 */
export function processDataBinding(template, data) {
  if (!template || !data) {
    return template || '';
  }
  
  let result = template;
  const regex = /\{\{([^}]+)\}\}/g;
  let match;
  
  while ((match = regex.exec(template)) !== null) {
    const placeholder = match[0];
    const fieldName = match[1].trim();
    const value = data[fieldName];
    const replacement = value !== undefined && value !== null ? String(value) : '';
    result = result.replace(placeholder, replacement);
  }
  
  return result;
}

/**
 * 验证元素配置
 * @param {Object} element 元素配置
 * @returns {Object} 验证结果 {valid, errors}
 */
export function validateElement(element) {
  const errors = [];
  
  if (!element.type) {
    errors.push('元素类型不能为空');
  }
  
  if (element.left === undefined || element.left === null) {
    errors.push('元素X坐标不能为空');
  }
  
  if (element.top === undefined || element.top === null) {
    errors.push('元素Y坐标不能为空');
  }
  
  // 根据元素类型进行特定验证
  switch (element.type) {
    case 'text':
      if (!element.text && !element.dataBinding) {
        errors.push('文本元素必须有内容或数据绑定');
      }
      break;
    case 'image':
      if (!element.src) {
        errors.push('图片元素必须有图片源');
      }
      break;
    case 'qrcode':
      if (!element.dataBinding && !element.text) {
        errors.push('二维码元素必须有数据绑定或文本内容');
      }
      break;
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 深拷贝对象
 * @param {*} obj 要拷贝的对象
 * @returns {*} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === 'object') {
    const cloned = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 生成随机颜色
 * @returns {string} 十六进制颜色值
 */
export function generateRandomColor() {
  return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
}

/**
 * 颜色格式转换：RGB转十六进制
 * @param {number} r 红色值 (0-255)
 * @param {number} g 绿色值 (0-255)
 * @param {number} b 蓝色值 (0-255)
 * @returns {string} 十六进制颜色值
 */
export function rgbToHex(r, g, b) {
  return '#' + [r, g, b].map(x => {
    const hex = x.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  }).join('');
}

/**
 * 颜色格式转换：十六进制转RGB
 * @param {string} hex 十六进制颜色值
 * @returns {Object} RGB对象 {r, g, b}
 */
export function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

/**
 * 获取对比色（黑色或白色）
 * @param {string} hex 十六进制颜色值
 * @returns {string} 对比色
 */
export function getContrastColor(hex) {
  const rgb = hexToRgb(hex);
  if (!rgb) return '#000000';
  
  // 计算亮度
  const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
  return brightness > 128 ? '#000000' : '#ffffff';
}

/**
 * 导出画布为图片
 * @param {Object} canvas Fabric.js画布实例
 * @param {Object} options 导出选项
 * @returns {string} 图片数据URL
 */
export function exportCanvasAsImage(canvas, options = {}) {
  const defaultOptions = {
    format: 'png',
    quality: 1,
    multiplier: 1
  };
  
  const exportOptions = { ...defaultOptions, ...options };
  
  return canvas.toDataURL({
    format: exportOptions.format,
    quality: exportOptions.quality,
    multiplier: exportOptions.multiplier
  });
}

/**
 * 限制数值在指定范围内
 * @param {number} value 数值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 限制后的数值
 */
export function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max);
}
