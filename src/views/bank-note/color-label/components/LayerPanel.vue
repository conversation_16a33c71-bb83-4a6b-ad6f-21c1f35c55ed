<template>
  <div class="layer-panel">
    <div class="panel-header">
      <h4>图层管理</h4>
      <div class="header-actions">
        <el-button size="small" @click="selectAll" title="全选">
          <el-icon><Select /></el-icon>
        </el-button>
        <el-button size="small" @click="clearSelection" title="清除选择">
          <el-icon><CircleClose /></el-icon>
        </el-button>
      </div>
    </div>

    <div class="panel-content">
      <!-- 图层列表 -->
      <div class="layer-list" v-if="elements.length > 0">
        <div
          v-for="(element, index) in sortedElements"
          :key="element.id"
          class="layer-item"
          :class="{
            'selected': selectedElement?.id === element.id,
            'locked': element.locked,
            'hidden': !element.visible
          }"
          @click="selectElement(element)"
          @contextmenu.prevent="showContextMenu($event, element)"
        >
          <!-- 图层图标 -->
          <div class="layer-icon">
            <el-icon v-if="element.type === 'text'"><EditPen /></el-icon>
            <el-icon v-else-if="element.type === 'image'"><Picture /></el-icon>
            <el-icon v-else-if="element.type === 'qrcode'"><Grid /></el-icon>
            <el-icon v-else-if="element.type === 'rectangle'"><Minus /></el-icon>
            <el-icon v-else-if="element.type === 'circle'"><CirclePlus /></el-icon>
            <el-icon v-else><Document /></el-icon>
          </div>

          <!-- 图层名称 -->
          <div class="layer-name" :title="element.name">
            {{ element.name }}
          </div>

          <!-- 图层控制 -->
          <div class="layer-controls">
            <el-button
              size="small"
              text
              :type="element.visible ? 'primary' : 'info'"
              @click.stop="toggleVisibility(element)"
              :title="element.visible ? '隐藏' : '显示'"
            >
              <el-icon>
                <View v-if="element.visible" />
                <Hide v-else />
              </el-icon>
            </el-button>

            <el-button
              size="small"
              text
              :type="element.locked ? 'warning' : 'info'"
              @click.stop="toggleLock(element)"
              :title="element.locked ? '解锁' : '锁定'"
            >
              <el-icon>
                <Lock v-if="element.locked" />
                <Unlock v-else />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-icon class="empty-icon"><DocumentAdd /></el-icon>
        <p>暂无图层</p>
        <p class="empty-tip">从左侧工具面板添加元素</p>
      </div>
    </div>

    <!-- 图层操作工具栏 -->
    <div class="layer-toolbar" v-if="elements.length > 0">
      <el-button-group size="small">
        <el-button
          @click="moveLayerUp"
          :disabled="!canMoveUp"
          title="上移图层"
        >
          <el-icon><ArrowUp /></el-icon>
        </el-button>
        <el-button
          @click="moveLayerDown"
          :disabled="!canMoveDown"
          title="下移图层"
        >
          <el-icon><ArrowDown /></el-icon>
        </el-button>
      </el-button-group>

      <el-divider direction="vertical" />

      <el-button-group size="small">
        <el-button
          @click="duplicateLayer"
          :disabled="!selectedElement"
          title="复制图层"
        >
          <el-icon><CopyDocument /></el-icon>
        </el-button>
        <el-button
          @click="deleteLayer"
          :disabled="!selectedElement"
          type="danger"
          title="删除图层"
        >
          <el-icon><Delete /></el-icon>
        </el-button>
      </el-button-group>
    </div>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      trigger="contextmenu"
      :teleported="false"
      @command="handleContextCommand"
    >
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="duplicate">
            <el-icon><CopyDocument /></el-icon>
            复制图层
          </el-dropdown-item>
          <el-dropdown-item command="delete" divided>
            <el-icon><Delete /></el-icon>
            删除图层
          </el-dropdown-item>
          <el-dropdown-item command="moveToTop" divided>
            <el-icon><Top /></el-icon>
            置于顶层
          </el-dropdown-item>
          <el-dropdown-item command="moveToBottom">
            <el-icon><Bottom /></el-icon>
            置于底层
          </el-dropdown-item>
          <el-dropdown-item command="toggleVisibility" divided>
            <el-icon><View /></el-icon>
            切换显示/隐藏
          </el-dropdown-item>
          <el-dropdown-item command="toggleLock">
            <el-icon><Lock /></el-icon>
            切换锁定/解锁
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
  import { ref, computed, inject } from 'vue';
  import {
    Select,
    CircleClose,
    EditPen,
    Picture,
    Grid,
    Minus,
    CirclePlus,
    Document,
    DocumentAdd,
    View,
    Hide,
    Lock,
    Unlock,
    ArrowUp,
    ArrowDown,
    CopyDocument,
    Delete,
    Top,
    Bottom
  } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';

  const props = defineProps({
    elements: {
      type: Array,
      default: () => []
    },
    selectedElement: {
      type: Object,
      default: null
    }
  });

  const emit = defineEmits([
    'select-element',
    'toggle-visibility',
    'toggle-lock',
    'delete-element',
    'duplicate-element',
    'move-layer'
  ]);

  // 注入画布实例
  const canvas = inject('canvas');
  const elementManager = inject('elementManager');

  // 响应式数据
  const contextMenuRef = ref(null);
  const contextMenuTarget = ref(null);

  // 计算属性
  const sortedElements = computed(() => {
    // 按照在画布中的层级顺序排序（顶层在前）
    return [...props.elements].reverse();
  });

  const selectedIndex = computed(() => {
    if (!props.selectedElement) return -1;
    return sortedElements.value.findIndex(el => el.id === props.selectedElement.id);
  });

  const canMoveUp = computed(() => {
    return props.selectedElement && selectedIndex.value > 0;
  });

  const canMoveDown = computed(() => {
    return props.selectedElement && selectedIndex.value < sortedElements.value.length - 1;
  });

  // 方法
  const selectElement = (element) => {
    emit('select-element', element);
  };

  const toggleVisibility = (element) => {
    element.visible = !element.visible;
    if (element.object) {
      element.object.set({ visible: element.visible });
      canvas.value?.renderAll();
    }
    emit('toggle-visibility', element);
  };

  const toggleLock = (element) => {
    element.locked = !element.locked;
    if (element.object) {
      element.object.set({
        selectable: !element.locked,
        evented: !element.locked
      });
      canvas.value?.renderAll();
    }
    emit('toggle-lock', element);
  };

  const moveLayerUp = () => {
    if (!props.selectedElement || !canMoveUp.value) return;

    if (canvas.value && props.selectedElement.object) {
      canvas.value.bringForward(props.selectedElement.object);
      canvas.value.renderAll();
    }

    emit('move-layer', { element: props.selectedElement, direction: 'up' });
  };

  const moveLayerDown = () => {
    if (!props.selectedElement || !canMoveDown.value) return;

    if (canvas.value && props.selectedElement.object) {
      canvas.value.sendBackwards(props.selectedElement.object);
      canvas.value.renderAll();
    }

    emit('move-layer', { element: props.selectedElement, direction: 'down' });
  };

  const duplicateLayer = () => {
    if (!props.selectedElement) return;

    emit('duplicate-element', props.selectedElement);
    EleMessage.success('图层已复制');
  };

  const deleteLayer = async () => {
    if (!props.selectedElement) return;

    try {
      await ElMessageBox.confirm(
        `确定要删除图层 "${props.selectedElement.name}" 吗？`,
        '删除确认',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      emit('delete-element', props.selectedElement);
      EleMessage.success('图层已删除');

    } catch {
      // 用户取消删除
    }
  };

  const selectAll = () => {
    if (!canvas.value || props.elements.length === 0) return;

    const objects = props.elements.map(el => el.object).filter(Boolean);
    if (objects.length > 0) {
      // 导入fabric
      import('fabric/dist/fabric.min.js').then(({ fabric }) => {
        const selection = new fabric.ActiveSelection(objects, {
          canvas: canvas.value
        });
        canvas.value.setActiveObject(selection);
        canvas.value.renderAll();
      });
    }
  };

  const clearSelection = () => {
    if (canvas.value) {
      canvas.value.discardActiveObject();
      canvas.value.renderAll();
    }
  };

  const showContextMenu = (event, element) => {
    contextMenuTarget.value = element;
    // 这里可以实现右键菜单的显示逻辑
    console.log('显示右键菜单:', element);
  };

  const handleContextCommand = (command) => {
    if (!contextMenuTarget.value) return;

    const element = contextMenuTarget.value;

    switch (command) {
      case 'duplicate':
        duplicateLayer();
        break;
      case 'delete':
        deleteLayer();
        break;
      case 'moveToTop':
        if (canvas.value && element.object) {
          canvas.value.bringToFront(element.object);
          canvas.value.renderAll();
        }
        break;
      case 'moveToBottom':
        if (canvas.value && element.object) {
          canvas.value.sendToBack(element.object);
          canvas.value.renderAll();
        }
        break;
      case 'toggleVisibility':
        toggleVisibility(element);
        break;
      case 'toggleLock':
        toggleLock(element);
        break;
    }

    contextMenuTarget.value = null;
  };
</script>

<style scoped>
  .layer-panel {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background-color: #fafafa;
  }

  .panel-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }

  .header-actions {
    display: flex;
    gap: 4px;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
  }

  .layer-list {
    padding: 8px 0;
  }

  .layer-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
  }

  .layer-item:hover {
    background-color: #f5f5f5;
  }

  .layer-item.selected {
    background-color: #e6f7ff;
    border-left-color: #1677ff;
  }

  .layer-item.locked {
    opacity: 0.6;
  }

  .layer-item.hidden {
    opacity: 0.4;
  }

  .layer-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: #666;
  }

  .layer-item.selected .layer-icon {
    color: #1677ff;
  }

  .layer-name {
    flex: 1;
    font-size: 12px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .layer-item.selected .layer-name {
    color: #1677ff;
    font-weight: 500;
  }

  .layer-controls {
    display: flex;
    gap: 2px;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .layer-item:hover .layer-controls,
  .layer-item.selected .layer-controls {
    opacity: 1;
  }

  .layer-controls .el-button {
    padding: 2px;
    width: 24px;
    height: 24px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-state p {
    margin: 4px 0;
    font-size: 14px;
  }

  .empty-tip {
    font-size: 12px !important;
    color: #ccc !important;
  }

  .layer-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-top: 1px solid #e8e8e8;
    background-color: #fafafa;
  }

  .layer-toolbar .el-button {
    padding: 4px 8px;
  }
</style>
