<template>
  <ele-page>
    <!-- 模板列表页面 -->
    <div v-if="!showDesigner" class="template-list-page">
      <!-- 页面标题和操作 -->
      <div class="page-header">
        <div class="header-left">
          <h2>彩签模板管理</h2>
          <p class="page-description">创建和管理彩签打印模板，支持文本、图片、二维码等元素的自由设计</p>
        </div>

        <div class="header-right">
          <el-button type="primary" @click="createNewTemplate">
            <el-icon><Plus /></el-icon>
            新建模板
          </el-button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <ele-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="模板名称">
            <el-input
              v-model="searchForm.templateName"
              placeholder="输入模板名称搜索"
              clearable
              style="width: 200px;"
            />
          </el-form-item>

          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 240px;"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </ele-card>

      <!-- 模板列表 -->
      <ele-card class="template-list-card">
        <div class="list-toolbar">
          <div class="toolbar-left">
            <span class="total-count">共 {{ pagination.total }} 个模板</span>
          </div>

          <div class="toolbar-right">
            <el-radio-group v-model="viewMode" size="small">
              <el-radio-button label="grid">
                <el-icon><Grid /></el-icon>
              </el-radio-button>
              <el-radio-button label="list">
                <el-icon><List /></el-icon>
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="template-grid">
          <div
            v-for="template in templateList"
            :key="template.id"
            class="template-card"
            @click="editTemplate(template)"
          >
            <div class="card-preview">
              <div class="preview-placeholder">
                <el-icon><DocumentCopy /></el-icon>
              </div>
            </div>

            <div class="card-content">
              <h4 class="template-name" :title="template.templateName">
                {{ template.templateName }}
              </h4>

              <div class="template-meta">
                <span class="meta-item">
                  <el-icon><Clock /></el-icon>
                  {{ formatDate(template.createTime) }}
                </span>
              </div>

              <div class="card-actions">
                <el-button size="small" @click.stop="previewTemplate(template)">
                  <el-icon><View /></el-icon>
                  预览
                </el-button>

                <el-dropdown @command="(cmd) => handleTemplateAction(cmd, template)" trigger="click">
                  <el-button size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item command="copy">
                        <el-icon><CopyDocument /></el-icon>
                        复制
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" divided>
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="templateList.length === 0" class="empty-state">
            <el-empty description="暂无彩签模板">
              <el-button type="primary" @click="createNewTemplate">
                创建第一个模板
              </el-button>
            </el-empty>
          </div>
        </div>

        <!-- 列表视图 -->
        <el-table v-else :data="templateList" stripe>
          <el-table-column prop="templateName" label="模板名称" min-width="200">
            <template #default="{ row }">
              <div class="template-name-cell">
                <el-icon class="template-icon"><DocumentCopy /></el-icon>
                <span>{{ row.templateName }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

          <el-table-column prop="createTime" label="创建时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column prop="updateTime" label="更新时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.updateTime) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="previewTemplate(row)">
                预览
              </el-button>
              <el-button size="small" @click="editTemplate(row)">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="deleteTemplate(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper" v-if="templateList.length > 0">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </ele-card>
    </div>

    <!-- 设计器页面 -->
    <ColorLabelDesigner
      v-else
      :template-id="currentTemplateId"
      :initial-template="currentTemplate"
      @back="handleBackToList"
      @saved="handleTemplateSaved"
    />
  </ele-page>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import {
    Plus,
    Search,
    Refresh,
    Grid,
    List,
    DocumentCopy,
    Clock,
    View,
    MoreFilled,
    Edit,
    CopyDocument,
    Delete
  } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import dayjs from 'dayjs';

  // 导入组件
  import ColorLabelDesigner from './components/ColorLabelDesigner.vue';

  // 导入API
  import {
    getColorLabelTemplates,
    getColorLabelTemplate,
    deleteColorLabelTemplate,
    copyColorLabelTemplate
  } from './api';

  // 响应式数据
  const showDesigner = ref(false);
  const currentTemplateId = ref(null);
  const currentTemplate = ref(null);
  const viewMode = ref('grid');
  const loading = ref(false);

  // 搜索表单
  const searchForm = reactive({
    templateName: '',
    dateRange: null
  });

  // 分页数据
  const pagination = reactive({
    current: 1,
    size: 20,
    total: 0
  });

  // 模板列表
  const templateList = ref([]);

  // 方法
  const loadTemplateList = async () => {
    loading.value = true;

    try {
      const params = {
        current: pagination.current,
        size: pagination.size,
        templateName: searchForm.templateName || undefined,
        startDate: searchForm.dateRange?.[0] || undefined,
        endDate: searchForm.dateRange?.[1] || undefined
      };

      const result = await getColorLabelTemplates(params);

      templateList.value = result.records || [];
      pagination.total = result.total || 0;

    } catch (error) {
      console.error('加载模板列表失败:', error);
      EleMessage.error('加载模板列表失败: ' + error.message);
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = () => {
    pagination.current = 1;
    loadTemplateList();
  };

  const handleReset = () => {
    searchForm.templateName = '';
    searchForm.dateRange = null;
    pagination.current = 1;
    loadTemplateList();
  };

  const handleSizeChange = (size) => {
    pagination.size = size;
    pagination.current = 1;
    loadTemplateList();
  };

  const handleCurrentChange = (current) => {
    pagination.current = current;
    loadTemplateList();
  };

  const createNewTemplate = () => {
    currentTemplateId.value = null;
    currentTemplate.value = null;
    showDesigner.value = true;
  };

  const editTemplate = async (template) => {
    try {
      // 加载完整的模板数据
      const fullTemplate = await getColorLabelTemplate(template.id);

      currentTemplateId.value = template.id;
      currentTemplate.value = fullTemplate;
      showDesigner.value = true;

    } catch (error) {
      console.error('加载模板详情失败:', error);
      EleMessage.error('加载模板详情失败: ' + error.message);
    }
  };

  const previewTemplate = (template) => {
    // 实现预览功能
    EleMessage.info('预览功能开发中...');
  };

  const handleTemplateAction = async (command, template) => {
    switch (command) {
      case 'edit':
        editTemplate(template);
        break;
      case 'copy':
        await copyTemplate(template);
        break;
      case 'delete':
        await deleteTemplate(template);
        break;
    }
  };

  const copyTemplate = async (template) => {
    try {
      const newName = `${template.templateName} - 副本`;
      await copyColorLabelTemplate(template.id, newName);

      EleMessage.success('模板复制成功');
      loadTemplateList();

    } catch (error) {
      console.error('复制模板失败:', error);
      EleMessage.error('复制模板失败: ' + error.message);
    }
  };

  const deleteTemplate = async (template) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除模板 "${template.templateName}" 吗？此操作不可恢复。`,
        '删除确认',
        {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      );

      await deleteColorLabelTemplate(template.id);

      EleMessage.success('模板删除成功');
      loadTemplateList();

    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除模板失败:', error);
        EleMessage.error('删除模板失败: ' + error.message);
      }
    }
  };

  const handleBackToList = () => {
    showDesigner.value = false;
    currentTemplateId.value = null;
    currentTemplate.value = null;
    // 刷新列表
    loadTemplateList();
  };

  const handleTemplateSaved = (template) => {
    EleMessage.success('模板保存成功');
    // 可以选择是否返回列表页面
    // handleBackToList();
  };

  const formatDate = (date) => {
    if (!date) return '-';
    return dayjs(date).format('YYYY-MM-DD HH:mm');
  };

  // 生命周期
  onMounted(() => {
    loadTemplateList();
  });
</script>

<style scoped>
  .template-list-page {
    padding: 0;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  .header-left h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 500;
    color: #333;
  }

  .page-description {
    margin: 0;
    font-size: 14px;
    color: #666;
  }

  .search-card {
    margin-bottom: 16px;
  }

  .template-list-card {
    margin-bottom: 16px;
  }

  .list-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .total-count {
    font-size: 14px;
    color: #666;
  }

  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    min-height: 200px;
  }

  .template-card {
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    background-color: #ffffff;
  }

  .template-card:hover {
    border-color: #1677ff;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.1);
    transform: translateY(-2px);
  }

  .card-preview {
    height: 120px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #e8e8e8;
  }

  .preview-placeholder {
    font-size: 32px;
    color: #ccc;
  }

  .card-content {
    padding: 16px;
  }

  .template-name {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .template-meta {
    margin-bottom: 12px;
  }

  .meta-item {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #999;
  }

  .card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .template-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .template-icon {
    color: #1677ff;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
</style>
