-- 创建彩签设计表
CREATE TABLE COLOR_LABEL_DESIGN (
    ID VARCHAR2(50) NOT NULL,
    DESIGN_NAME VARCHAR2(100) NOT NULL,
    BASE_TEMPLATE_ID VARCHAR2(50) NOT NULL,
    SENDFORM_NUMBER VARCHAR2(50),
    COIN_IDS CLOB,
    COLOR_ELEMENTS CLOB,
    COMPOSED_TEMPLATE CLOB,
    DESIGN_TYPE VARCHAR2(20) DEFAULT 'BATCH',
    STATUS VARCHAR2(10) DEFAULT 'DRAFT',
    PREVIEW_IMAGE VARCHAR2(500),
    USE_COUNT NUMBER(10) DEFAULT 0,
    LAST_USED_TIME DATE,
    DESCRIPTION VARCHAR2(500),
    CREATE_USER VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE,
    UPDATE_USER VARCHAR2(50),
    UPDATE_TIME DATE DEFAULT SYSDATE,
    VERSION NUMBER(10) DEFAULT 1,
    DELETED NUMBER(1) DEFAULT 0,
    EXT_FIELD1 VARCHAR2(200),
    EXT_FIELD2 VARCHAR2(200),
    EXT_FIELD3 CLOB,
    CONSTRAINT PK_COLOR_LABEL_DESIGN PRIMARY KEY (ID)
);

-- 创建索引
CREATE INDEX IDX_COLOR_LABEL_DESIGN_NAME ON COLOR_LABEL_DESIGN(DESIGN_NAME);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_TEMPLATE ON COLOR_LABEL_DESIGN(BASE_TEMPLATE_ID);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_SENDFORM ON COLOR_LABEL_DESIGN(SENDFORM_NUMBER);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_TYPE ON COLOR_LABEL_DESIGN(DESIGN_TYPE);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_STATUS ON COLOR_LABEL_DESIGN(STATUS);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_CREATE_TIME ON COLOR_LABEL_DESIGN(CREATE_TIME);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_CREATE_USER ON COLOR_LABEL_DESIGN(CREATE_USER);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_DELETED ON COLOR_LABEL_DESIGN(DELETED);

-- 添加表注释
COMMENT ON TABLE COLOR_LABEL_DESIGN IS '彩签设计表';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.ID IS '主键ID';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.DESIGN_NAME IS '设计名称';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.BASE_TEMPLATE_ID IS '基础标签模板ID';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.SENDFORM_NUMBER IS '送评单号';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.COIN_IDS IS '关联的钱币ID列表（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.COLOR_ELEMENTS IS '彩签元素配置（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.COMPOSED_TEMPLATE IS '合成后的完整模板配置（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.DESIGN_TYPE IS '设计类型：SINGLE-单个钱币，BATCH-批量钱币，TEMPLATE-设计模板';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.STATUS IS '设计状态：DRAFT-草稿，SAVED-已保存，TEMPLATE-作为模板';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.PREVIEW_IMAGE IS '预览图片URL';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.USE_COUNT IS '使用次数';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.LAST_USED_TIME IS '最后使用时间';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.DESCRIPTION IS '设计描述';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.CREATE_USER IS '创建用户';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.UPDATE_USER IS '更新用户';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.VERSION IS '版本号（用于乐观锁）';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.DELETED IS '逻辑删除标识：0-未删除，1-已删除';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.EXT_FIELD1 IS '扩展字段1';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.EXT_FIELD2 IS '扩展字段2';
COMMENT ON COLUMN COLOR_LABEL_DESIGN.EXT_FIELD3 IS '扩展字段3（JSON格式）';

-- 创建彩签设计使用记录表
CREATE TABLE COLOR_LABEL_DESIGN_USAGE (
    ID VARCHAR2(50) NOT NULL,
    DESIGN_ID VARCHAR2(50) NOT NULL,
    SENDFORM_NUMBER VARCHAR2(50),
    COIN_IDS CLOB,
    PRINT_TIME DATE DEFAULT SYSDATE,
    PRINT_COUNT NUMBER(10) DEFAULT 0,
    CREATE_USER VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE,
    EXT_FIELD1 VARCHAR2(200),
    EXT_FIELD2 VARCHAR2(200),
    CONSTRAINT PK_COLOR_LABEL_DESIGN_USAGE PRIMARY KEY (ID)
);

-- 创建索引
CREATE INDEX IDX_COLOR_LABEL_DESIGN_USAGE_DESIGN ON COLOR_LABEL_DESIGN_USAGE(DESIGN_ID);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_USAGE_SENDFORM ON COLOR_LABEL_DESIGN_USAGE(SENDFORM_NUMBER);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_USAGE_PRINT_TIME ON COLOR_LABEL_DESIGN_USAGE(PRINT_TIME);
CREATE INDEX IDX_COLOR_LABEL_DESIGN_USAGE_CREATE_USER ON COLOR_LABEL_DESIGN_USAGE(CREATE_USER);

-- 添加表注释
COMMENT ON TABLE COLOR_LABEL_DESIGN_USAGE IS '彩签设计使用记录表';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.ID IS '主键ID';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.DESIGN_ID IS '彩签设计ID';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.SENDFORM_NUMBER IS '送评单号';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.COIN_IDS IS '钱币ID列表（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.PRINT_TIME IS '打印时间';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.PRINT_COUNT IS '打印数量';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.CREATE_USER IS '创建用户';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.EXT_FIELD1 IS '扩展字段1';
COMMENT ON COLUMN COLOR_LABEL_DESIGN_USAGE.EXT_FIELD2 IS '扩展字段2';

-- 插入示例彩签设计
INSERT INTO COLOR_LABEL_DESIGN (
    ID, 
    DESIGN_NAME, 
    BASE_TEMPLATE_ID, 
    SENDFORM_NUMBER,
    COIN_IDS,
    COLOR_ELEMENTS, 
    COMPOSED_TEMPLATE,
    DESIGN_TYPE,
    STATUS, 
    DESCRIPTION,
    CREATE_USER,
    CREATE_TIME
) VALUES (
    'SAMPLE_COLOR_DESIGN_001',
    '示例彩签设计',
    'DEFAULT_LABEL_TEMPLATE_001', -- 假设存在默认的标签模板
    'SF202501150001',
    '["COIN001", "COIN002", "COIN003"]',
    '[{"id":"text_001","type":"text","name":"客户名称","text":"{{customerName}}","left":20,"top":10,"fontSize":14,"fill":"#ff0000","dataBinding":"customerName"},{"id":"qrcode_001","type":"qrcode","name":"二维码","dataBinding":"{{diyCode}}","left":150,"top":5,"width":40,"height":40}]',
    '{"baseTemplate":{},"colorElements":[]}',
    'BATCH',
    'SAVED',
    '基于标签模板扩展的彩签设计示例',
    'SYSTEM',
    SYSDATE
);

-- 插入权限配置
INSERT INTO SYS_MENU (
    MENU_ID, 
    PARENT_ID, 
    TITLE, 
    PATH, 
    COMPONENT, 
    MENU_TYPE, 
    SORT_NUMBER, 
    AUTHORITY, 
    TARGET, 
    ICON, 
    COLOR, 
    HIDE, 
    META, 
    CREATE_TIME, 
    UPDATE_TIME
) VALUES (
    'MENU_COLOR_LABEL_DESIGN_001',
    'MENU_BANK_NOTE_ROOT', -- 假设存在银行票据根菜单
    '彩签设计',
    '/bank-note/color-label-design',
    '/bank-note/color-label-design',
    0, -- 菜单类型：0-菜单，1-按钮
    31, -- 排序号
    'banknote:colorlabel:design',
    '_self',
    'el-icon-brush',
    NULL,
    0, -- 是否隐藏：0-显示，1-隐藏
    '{"title":"彩签设计","icon":"el-icon-brush"}',
    SYSDATE,
    SYSDATE
);

-- 插入彩签设计按钮权限
INSERT INTO SYS_MENU (
    MENU_ID, 
    PARENT_ID, 
    TITLE, 
    PATH, 
    COMPONENT, 
    MENU_TYPE, 
    SORT_NUMBER, 
    AUTHORITY, 
    TARGET, 
    ICON, 
    COLOR, 
    HIDE, 
    META, 
    CREATE_TIME, 
    UPDATE_TIME
) VALUES 
-- 设计权限
(
    'MENU_COLOR_LABEL_DESIGN_CREATE',
    'MENU_COLOR_LABEL_DESIGN_001',
    '创建彩签设计',
    NULL,
    NULL,
    1, -- 按钮类型
    1,
    'banknote:colorlabel:design',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"创建彩签设计"}',
    SYSDATE,
    SYSDATE
),
-- 预览权限
(
    'MENU_COLOR_LABEL_DESIGN_PREVIEW',
    'MENU_COLOR_LABEL_DESIGN_001',
    '预览彩签设计',
    NULL,
    NULL,
    1,
    2,
    'banknote:colorlabel:preview',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"预览彩签设计"}',
    SYSDATE,
    SYSDATE
),
-- 打印权限
(
    'MENU_COLOR_LABEL_DESIGN_PRINT',
    'MENU_COLOR_LABEL_DESIGN_001',
    '彩签批量打印',
    NULL,
    NULL,
    1,
    3,
    'banknote:colorlabel:print',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"彩签批量打印"}',
    SYSDATE,
    SYSDATE
);

-- 为管理员角色分配彩签设计权限
INSERT INTO SYS_ROLE_MENU (
    ROLE_ID, 
    MENU_ID
) 
SELECT 
    'ROLE_ADMIN', -- 管理员角色ID，需要根据实际情况调整
    MENU_ID
FROM SYS_MENU 
WHERE MENU_ID LIKE 'MENU_COLOR_LABEL_DESIGN_%';

-- 提交事务
COMMIT;
