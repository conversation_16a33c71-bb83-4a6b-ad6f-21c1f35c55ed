-- 插入彩签模板管理相关权限
-- 注意：这里的权限ID和菜单ID需要根据实际系统的ID生成规则调整

-- 插入彩签模板管理菜单
INSERT INTO SYS_MENU (
    MENU_ID, 
    PARENT_ID, 
    TITLE, 
    PATH, 
    COMPONENT, 
    MENU_TYPE, 
    SORT_NUMBER, 
    AUTHORITY, 
    TARGET, 
    ICON, 
    COLOR, 
    HIDE, 
    META, 
    CREATE_TIME, 
    UPDATE_TIME
) VALUES (
    'MENU_COLOR_LABEL_001',
    'MENU_BANK_NOTE_ROOT', -- 假设存在银行票据根菜单
    '彩签模板管理',
    '/bank-note/color-label',
    '/bank-note/color-label',
    0, -- 菜单类型：0-菜单，1-按钮
    30, -- 排序号
    'banknote:colorlabel:list',
    '_self',
    'el-icon-edit',
    NULL,
    0, -- 是否隐藏：0-显示，1-隐藏
    '{"title":"彩签模板管理","icon":"el-icon-edit"}',
    SYSDATE,
    SYSDATE
);

-- 插入彩签模板管理按钮权限
INSERT INTO SYS_MENU (
    MENU_ID, 
    PARENT_ID, 
    TITLE, 
    PATH, 
    COMPONENT, 
    MENU_TYPE, 
    SORT_NUMBER, 
    AUTHORITY, 
    TARGET, 
    ICON, 
    COLOR, 
    HIDE, 
    META, 
    CREATE_TIME, 
    UPDATE_TIME
) VALUES 
-- 查看权限
(
    'MENU_COLOR_LABEL_LIST',
    'MENU_COLOR_LABEL_001',
    '查看彩签模板',
    NULL,
    NULL,
    1, -- 按钮类型
    1,
    'banknote:colorlabel:list',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"查看彩签模板"}',
    SYSDATE,
    SYSDATE
),
-- 新增权限
(
    'MENU_COLOR_LABEL_ADD',
    'MENU_COLOR_LABEL_001',
    '新增彩签模板',
    NULL,
    NULL,
    1,
    2,
    'banknote:colorlabel:save',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"新增彩签模板"}',
    SYSDATE,
    SYSDATE
),
-- 编辑权限
(
    'MENU_COLOR_LABEL_EDIT',
    'MENU_COLOR_LABEL_001',
    '编辑彩签模板',
    NULL,
    NULL,
    1,
    3,
    'banknote:colorlabel:update',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"编辑彩签模板"}',
    SYSDATE,
    SYSDATE
),
-- 删除权限
(
    'MENU_COLOR_LABEL_DELETE',
    'MENU_COLOR_LABEL_001',
    '删除彩签模板',
    NULL,
    NULL,
    1,
    4,
    'banknote:colorlabel:delete',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"删除彩签模板"}',
    SYSDATE,
    SYSDATE
),
-- 预览权限
(
    'MENU_COLOR_LABEL_PREVIEW',
    'MENU_COLOR_LABEL_001',
    '预览彩签模板',
    NULL,
    NULL,
    1,
    5,
    'banknote:colorlabel:preview',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"预览彩签模板"}',
    SYSDATE,
    SYSDATE
),
-- 打印权限
(
    'MENU_COLOR_LABEL_PRINT',
    'MENU_COLOR_LABEL_001',
    '彩签批量打印',
    NULL,
    NULL,
    1,
    6,
    'banknote:colorlabel:print',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"彩签批量打印"}',
    SYSDATE,
    SYSDATE
),
-- 上传权限
(
    'MENU_COLOR_LABEL_UPLOAD',
    'MENU_COLOR_LABEL_001',
    '上传彩签素材',
    NULL,
    NULL,
    1,
    7,
    'banknote:colorlabel:upload',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"上传彩签素材"}',
    SYSDATE,
    SYSDATE
),
-- 导入权限
(
    'MENU_COLOR_LABEL_IMPORT',
    'MENU_COLOR_LABEL_001',
    '导入彩签模板',
    NULL,
    NULL,
    1,
    8,
    'banknote:colorlabel:import',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"导入彩签模板"}',
    SYSDATE,
    SYSDATE
),
-- 导出权限
(
    'MENU_COLOR_LABEL_EXPORT',
    'MENU_COLOR_LABEL_001',
    '导出彩签模板',
    NULL,
    NULL,
    1,
    9,
    'banknote:colorlabel:export',
    '_self',
    NULL,
    NULL,
    0,
    '{"title":"导出彩签模板"}',
    SYSDATE,
    SYSDATE
);

-- 为管理员角色分配彩签模板管理权限
-- 注意：这里的角色ID需要根据实际系统调整
INSERT INTO SYS_ROLE_MENU (
    ROLE_ID, 
    MENU_ID
) 
SELECT 
    'ROLE_ADMIN', -- 管理员角色ID，需要根据实际情况调整
    MENU_ID
FROM SYS_MENU 
WHERE MENU_ID LIKE 'MENU_COLOR_LABEL_%';

-- 创建彩签模板管理相关的数据字典
INSERT INTO SYS_DICTIONARY (
    DICT_ID,
    DICT_CODE,
    DICT_NAME,
    SORT_NUMBER,
    COMMENTS,
    CREATE_TIME,
    UPDATE_TIME
) VALUES (
    'DICT_COLOR_LABEL_001',
    'color_label_template_type',
    '彩签模板类型',
    1,
    '彩签模板的类型分类',
    SYSDATE,
    SYSDATE
);

-- 插入彩签模板类型字典项
INSERT INTO SYS_DICTIONARY_DATA (
    DICT_DATA_ID,
    DICT_ID,
    DICT_CODE,
    DICT_LABEL,
    DICT_VALUE,
    SORT_NUMBER,
    IS_DEFAULT,
    COMMENTS,
    CREATE_TIME,
    UPDATE_TIME
) VALUES 
(
    'DICT_DATA_COLOR_LABEL_001',
    'DICT_COLOR_LABEL_001',
    'color_label_template_type',
    '彩签模板',
    'COLOR_LABEL',
    1,
    1,
    '标准彩签模板',
    SYSDATE,
    SYSDATE
),
(
    'DICT_DATA_COLOR_LABEL_002',
    'DICT_COLOR_LABEL_001',
    'color_label_template_type',
    'Logo模板',
    'LOGO_TEMPLATE',
    2,
    0,
    'Logo图片模板',
    SYSDATE,
    SYSDATE
),
(
    'DICT_DATA_COLOR_LABEL_003',
    'DICT_COLOR_LABEL_001',
    'color_label_template_type',
    '文本模板',
    'TEXT_TEMPLATE',
    3,
    0,
    '纯文本模板',
    SYSDATE,
    SYSDATE
);

-- 创建彩签模板状态字典
INSERT INTO SYS_DICTIONARY (
    DICT_ID,
    DICT_CODE,
    DICT_NAME,
    SORT_NUMBER,
    COMMENTS,
    CREATE_TIME,
    UPDATE_TIME
) VALUES (
    'DICT_COLOR_LABEL_002',
    'color_label_template_status',
    '彩签模板状态',
    2,
    '彩签模板的状态',
    SYSDATE,
    SYSDATE
);

-- 插入彩签模板状态字典项
INSERT INTO SYS_DICTIONARY_DATA (
    DICT_DATA_ID,
    DICT_ID,
    DICT_CODE,
    DICT_LABEL,
    DICT_VALUE,
    SORT_NUMBER,
    IS_DEFAULT,
    COMMENTS,
    CREATE_TIME,
    UPDATE_TIME
) VALUES 
(
    'DICT_DATA_COLOR_LABEL_STATUS_001',
    'DICT_COLOR_LABEL_002',
    'color_label_template_status',
    '启用',
    'ACTIVE',
    1,
    1,
    '模板已启用',
    SYSDATE,
    SYSDATE
),
(
    'DICT_DATA_COLOR_LABEL_STATUS_002',
    'DICT_COLOR_LABEL_002',
    'color_label_template_status',
    '禁用',
    'INACTIVE',
    2,
    0,
    '模板已禁用',
    SYSDATE,
    SYSDATE
),
(
    'DICT_DATA_COLOR_LABEL_STATUS_003',
    'DICT_COLOR_LABEL_002',
    'color_label_template_status',
    '草稿',
    'DRAFT',
    3,
    0,
    '模板草稿状态',
    SYSDATE,
    SYSDATE
);

-- 提交事务
COMMIT;
