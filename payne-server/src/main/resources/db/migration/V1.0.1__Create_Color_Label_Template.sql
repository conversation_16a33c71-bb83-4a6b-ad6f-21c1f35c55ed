-- 创建彩签模板表
CREATE TABLE COLOR_LABEL_TEMPLATE (
    ID VARCHAR2(50) NOT NULL,
    TEMPLATE_NAME VARCHAR2(100) NOT NULL,
    TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'COLOR_LABEL',
    CANVAS_CONFIG CLOB,
    CANVAS_DATA CLOB,
    ELEMENTS CLOB,
    PREVIEW_IMAGE VARCHAR2(500),
    IS_DEFAULT NUMBER(1) DEFAULT 0,
    STATUS VARCHAR2(10) DEFAULT 'DRAFT',
    DESCRIPTION VARCHAR2(500),
    USE_COUNT NUMBER(10) DEFAULT 0,
    LAST_USED_TIME DATE,
    CREATE_USER VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE,
    UPDATE_USER VARCHAR2(50),
    UPDATE_TIME DATE DEFAULT SYSDATE,
    VERSION NUMBER(10) DEFAULT 1,
    DELETED NUMBER(1) DEFAULT 0,
    EXT_FIELD1 VARCHAR2(200),
    EXT_FIELD2 VARCHAR2(200),
    EXT_FIELD3 CLOB,
    CONSTRAINT PK_COLOR_LABEL_TEMPLATE PRIMARY KEY (ID)
);

-- 创建索引
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_NAME ON COLOR_LABEL_TEMPLATE(TEMPLATE_NAME);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_TYPE ON COLOR_LABEL_TEMPLATE(TEMPLATE_TYPE);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_STATUS ON COLOR_LABEL_TEMPLATE(STATUS);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_DEFAULT ON COLOR_LABEL_TEMPLATE(IS_DEFAULT);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_CREATE_TIME ON COLOR_LABEL_TEMPLATE(CREATE_TIME);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_CREATE_USER ON COLOR_LABEL_TEMPLATE(CREATE_USER);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_USE_COUNT ON COLOR_LABEL_TEMPLATE(USE_COUNT);
CREATE INDEX IDX_COLOR_LABEL_TEMPLATE_DELETED ON COLOR_LABEL_TEMPLATE(DELETED);

-- 添加表注释
COMMENT ON TABLE COLOR_LABEL_TEMPLATE IS '彩签模板表';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.ID IS '主键ID';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.TEMPLATE_NAME IS '模板名称';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.TEMPLATE_TYPE IS '模板类型：COLOR_LABEL-彩签模板，LOGO_TEMPLATE-Logo模板，TEXT_TEMPLATE-文本模板';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.CANVAS_CONFIG IS '画布配置（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.CANVAS_DATA IS '画布数据（JSON格式，Fabric.js导出的数据）';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.ELEMENTS IS '元素列表（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.PREVIEW_IMAGE IS '预览图片URL';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.IS_DEFAULT IS '是否为默认模板：0-否，1-是';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.STATUS IS '模板状态：ACTIVE-启用，INACTIVE-禁用，DRAFT-草稿';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.DESCRIPTION IS '模板描述';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.USE_COUNT IS '使用次数';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.LAST_USED_TIME IS '最后使用时间';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.CREATE_USER IS '创建用户';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.UPDATE_USER IS '更新用户';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.VERSION IS '版本号（用于乐观锁）';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.DELETED IS '逻辑删除标识：0-未删除，1-已删除';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.EXT_FIELD1 IS '扩展字段1';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.EXT_FIELD2 IS '扩展字段2';
COMMENT ON COLUMN COLOR_LABEL_TEMPLATE.EXT_FIELD3 IS '扩展字段3（JSON格式）';

-- 创建彩签使用记录表
CREATE TABLE COLOR_LABEL_USAGE (
    ID VARCHAR2(50) NOT NULL,
    TEMPLATE_ID VARCHAR2(50) NOT NULL,
    COIN_IDS CLOB,
    BASE_TEMPLATE_ID VARCHAR2(50),
    PRINT_TIME DATE DEFAULT SYSDATE,
    PRINT_COUNT NUMBER(10) DEFAULT 0,
    CREATE_USER VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE,
    EXT_FIELD1 VARCHAR2(200),
    EXT_FIELD2 VARCHAR2(200),
    CONSTRAINT PK_COLOR_LABEL_USAGE PRIMARY KEY (ID)
);

-- 创建索引
CREATE INDEX IDX_COLOR_LABEL_USAGE_TEMPLATE ON COLOR_LABEL_USAGE(TEMPLATE_ID);
CREATE INDEX IDX_COLOR_LABEL_USAGE_PRINT_TIME ON COLOR_LABEL_USAGE(PRINT_TIME);
CREATE INDEX IDX_COLOR_LABEL_USAGE_CREATE_USER ON COLOR_LABEL_USAGE(CREATE_USER);

-- 添加表注释
COMMENT ON TABLE COLOR_LABEL_USAGE IS '彩签使用记录表';
COMMENT ON COLUMN COLOR_LABEL_USAGE.ID IS '主键ID';
COMMENT ON COLUMN COLOR_LABEL_USAGE.TEMPLATE_ID IS '彩签模板ID';
COMMENT ON COLUMN COLOR_LABEL_USAGE.COIN_IDS IS '钱币ID列表（JSON格式）';
COMMENT ON COLUMN COLOR_LABEL_USAGE.BASE_TEMPLATE_ID IS '基础模板ID';
COMMENT ON COLUMN COLOR_LABEL_USAGE.PRINT_TIME IS '打印时间';
COMMENT ON COLUMN COLOR_LABEL_USAGE.PRINT_COUNT IS '打印数量';
COMMENT ON COLUMN COLOR_LABEL_USAGE.CREATE_USER IS '创建用户';
COMMENT ON COLUMN COLOR_LABEL_USAGE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN COLOR_LABEL_USAGE.EXT_FIELD1 IS '扩展字段1';
COMMENT ON COLUMN COLOR_LABEL_USAGE.EXT_FIELD2 IS '扩展字段2';

-- 插入默认彩签模板
INSERT INTO COLOR_LABEL_TEMPLATE (
    ID, 
    TEMPLATE_NAME, 
    TEMPLATE_TYPE, 
    CANVAS_CONFIG, 
    CANVAS_DATA, 
    ELEMENTS, 
    IS_DEFAULT, 
    STATUS, 
    DESCRIPTION,
    CREATE_USER,
    CREATE_TIME
) VALUES (
    'DEFAULT_COLOR_LABEL_001',
    '默认彩签模板',
    'COLOR_LABEL',
    '{"width":576,"height":78,"backgroundColor":"#ffffff","scale":3}',
    '{"version":"5.3.0","objects":[]}',
    '[]',
    1,
    'ACTIVE',
    '系统默认的彩签模板，包含基础的文本和二维码元素',
    'SYSTEM',
    SYSDATE
);

-- 插入示例彩签模板
INSERT INTO COLOR_LABEL_TEMPLATE (
    ID, 
    TEMPLATE_NAME, 
    TEMPLATE_TYPE, 
    CANVAS_CONFIG, 
    CANVAS_DATA, 
    ELEMENTS, 
    IS_DEFAULT, 
    STATUS, 
    DESCRIPTION,
    CREATE_USER,
    CREATE_TIME
) VALUES (
    'SAMPLE_COLOR_LABEL_001',
    '简单文本彩签',
    'COLOR_LABEL',
    '{"width":576,"height":78,"backgroundColor":"#ffffff","scale":3}',
    '{"version":"5.3.0","objects":[]}',
    '[{"id":"text_001","type":"text","text":"{{coinName}}","left":20,"top":10,"fontSize":14,"fontWeight":"bold","fill":"#000000","dataBinding":"coinName"},{"id":"text_002","type":"text","text":"{{serialNumber}}","left":20,"top":35,"fontSize":12,"fill":"#666666","dataBinding":"serialNumber"}]',
    0,
    'ACTIVE',
    '包含钱币名称和编号的简单文本彩签模板',
    'SYSTEM',
    SYSDATE
);

INSERT INTO COLOR_LABEL_TEMPLATE (
    ID, 
    TEMPLATE_NAME, 
    TEMPLATE_TYPE, 
    CANVAS_CONFIG, 
    CANVAS_DATA, 
    ELEMENTS, 
    IS_DEFAULT, 
    STATUS, 
    DESCRIPTION,
    CREATE_USER,
    CREATE_TIME
) VALUES (
    'SAMPLE_COLOR_LABEL_002',
    '带二维码彩签',
    'COLOR_LABEL',
    '{"width":576,"height":78,"backgroundColor":"#ffffff","scale":3}',
    '{"version":"5.3.0","objects":[]}',
    '[{"id":"text_001","type":"text","text":"{{coinName}}","left":20,"top":10,"fontSize":12,"fill":"#000000","dataBinding":"coinName"},{"id":"qrcode_001","type":"qrcode","dataBinding":"{{diyCode}}","left":120,"top":5,"width":50,"height":50}]',
    0,
    'ACTIVE',
    '包含钱币信息和二维码的彩签模板',
    'SYSTEM',
    SYSDATE
);

-- 提交事务
COMMIT;
