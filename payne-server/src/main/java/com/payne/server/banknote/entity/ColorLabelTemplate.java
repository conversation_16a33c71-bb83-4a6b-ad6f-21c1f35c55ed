package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 彩签模板实体类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Entity
@Table(name = "COLOR_LABEL_TEMPLATE")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("COLOR_LABEL_TEMPLATE")
public class ColorLabelTemplate implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 模板名称
     */
    @Column(name = "TEMPLATE_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("TEMPLATE_NAME")
    private String templateName;
    
    /**
     * 模板类型
     */
    @Column(name = "TEMPLATE_TYPE", columnDefinition = "VARCHAR2(20)")
    @TableField("TEMPLATE_TYPE")
    private String templateType;
    
    /**
     * 画布配置（JSON格式）
     */
    @Column(name = "CANVAS_CONFIG", columnDefinition = "CLOB")
    @TableField("CANVAS_CONFIG")
    private String canvasConfig;
    
    /**
     * 画布数据（JSON格式，Fabric.js导出的数据）
     */
    @Column(name = "CANVAS_DATA", columnDefinition = "CLOB")
    @TableField("CANVAS_DATA")
    private String canvasData;
    
    /**
     * 元素列表（JSON格式）
     */
    @Column(name = "ELEMENTS", columnDefinition = "CLOB")
    @TableField("ELEMENTS")
    private String elements;
    
    /**
     * 预览图片URL
     */
    @Column(name = "PREVIEW_IMAGE", columnDefinition = "VARCHAR2(500)")
    @TableField("PREVIEW_IMAGE")
    private String previewImage;
    
    /**
     * 是否为默认模板
     */
    @Column(name = "IS_DEFAULT", columnDefinition = "NUMBER(1)")
    @TableField("IS_DEFAULT")
    private Boolean isDefault;
    
    /**
     * 模板状态：ACTIVE-启用，INACTIVE-禁用
     */
    @Column(name = "STATUS", columnDefinition = "VARCHAR2(10)")
    @TableField("STATUS")
    private String status;
    
    /**
     * 模板描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR2(500)")
    @TableField("DESCRIPTION")
    private String description;
    
    /**
     * 使用次数
     */
    @Column(name = "USE_COUNT", columnDefinition = "NUMBER(10)")
    @TableField("USE_COUNT")
    private Integer useCount;
    
    /**
     * 最后使用时间
     */
    @Column(name = "LAST_USED_TIME")
    @TableField("LAST_USED_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUsedTime;
    
    /**
     * 创建用户
     */
    @Column(name = "CREATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("CREATE_USER")
    private String createUser;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新用户
     */
    @Column(name = "UPDATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("UPDATE_USER")
    private String updateUser;
    
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 版本号（用于乐观锁）
     */
    @Column(name = "VERSION", columnDefinition = "NUMBER(10)")
    @TableField("VERSION")
    @Version
    private Integer version;
    
    /**
     * 逻辑删除标识：0-未删除，1-已删除
     */
    @Column(name = "DELETED", columnDefinition = "NUMBER(1)")
    @TableField("DELETED")
    @TableLogic
    private Integer deleted;
    
    /**
     * 扩展字段1
     */
    @Column(name = "EXT_FIELD1", columnDefinition = "VARCHAR2(200)")
    @TableField("EXT_FIELD1")
    private String extField1;
    
    /**
     * 扩展字段2
     */
    @Column(name = "EXT_FIELD2", columnDefinition = "VARCHAR2(200)")
    @TableField("EXT_FIELD2")
    private String extField2;
    
    /**
     * 扩展字段3（JSON格式，用于存储额外配置）
     */
    @Column(name = "EXT_FIELD3", columnDefinition = "CLOB")
    @TableField("EXT_FIELD3")
    private String extField3;
}
