package com.payne.server.banknote.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.server.banknote.entity.ColorLabelTemplate;
import com.payne.server.banknote.service.ColorLabelService;
import com.payne.server.banknote.vo.ColorLabelTemplateVO;
import com.payne.server.banknote.dto.ColorLabelTemplateDto;
import com.payne.server.banknote.param.ColorLabelTemplateParam;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 彩签模板控制器
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/color-label")
@RequiredArgsConstructor
public class ColorLabelController extends BaseController {

    private final ColorLabelService colorLabelService;

    /**
     * 分页查询彩签模板列表
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/templates")
    public ApiResult<PageResult<ColorLabelTemplateVO>> getTemplates(ColorLabelTemplateParam param) {
        PageParam<ColorLabelTemplate, ColorLabelTemplateParam> page = new PageParam<>(param);
        page.setDefaultOrder("create_time desc");
        IPage<ColorLabelTemplateVO> result = colorLabelService.pageTemplates(page, page.getWrapper());
        return success(result);
    }

    /**
     * 根据ID获取彩签模板详情
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/templates/{id}")
    public ApiResult<?> getTemplate(@PathVariable("id") String id) {
        ColorLabelTemplateVO template = colorLabelService.getTemplateById(id);
        if (template == null) {
            return fail("模板不存在");
        }
        return success(template);
    }

    /**
     * 保存彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:save')")
    @OperationLog
    @PostMapping("/templates")
    public ApiResult<?> saveTemplate(@RequestBody ColorLabelTemplateDto templateDto) {
        try {
            ColorLabelTemplateVO result = colorLabelService.saveTemplate(templateDto);
            return success(result);
        } catch (Exception e) {
            return fail("保存失败：" + e.getMessage());
        }
    }

    /**
     * 更新彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:update')")
    @OperationLog
    @PutMapping("/templates/{id}")
    public ApiResult<?> updateTemplate(
            @PathVariable("id") String id,
            @RequestBody ColorLabelTemplateDto templateDto) {
        try {
            templateDto.setId(id);
            ColorLabelTemplateVO result = colorLabelService.updateTemplate(templateDto);
            return success(result);
        } catch (Exception e) {
            return fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:delete')")
    @OperationLog
    @DeleteMapping("/templates/{id}")
    public ApiResult<?> deleteTemplate(@PathVariable("id") String id) {
        try {
            boolean result = colorLabelService.deleteTemplate(id);
            if (result) {
                return success("删除成功");
            } else {
                return fail("删除失败");
            }
        } catch (Exception e) {
            return fail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 复制彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:save')")
    @OperationLog
    @PostMapping("/templates/{id}/copy")
    public ApiResult<?> copyTemplate(
            @PathVariable("id") String id,
            @RequestBody Map<String, String> params) {
        try {
            String newName = params.get("newName");
            if (newName == null || newName.trim().isEmpty()) {
                return fail("新模板名称不能为空");
            }
            
            ColorLabelTemplateVO result = colorLabelService.copyTemplate(id, newName);
            return success(result);
        } catch (Exception e) {
            return fail("复制失败：" + e.getMessage());
        }
    }

    /**
     * 预览彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:preview')")
    @OperationLog
    @PostMapping("/preview")
    public ApiResult<?> previewTemplate(@RequestBody Map<String, Object> params) {
        try {
            String templateId = (String) params.get("templateId");
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return fail("模板ID不能为空");
            }
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            Map<String, Object> result = colorLabelService.previewTemplate(templateId, coinIds);
            return success(result);
        } catch (Exception e) {
            return fail("预览失败：" + e.getMessage());
        }
    }

    /**
     * 生成彩签批量打印数据
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:print')")
    @OperationLog
    @PostMapping("/generate-print-data")
    public ApiResult<?> generatePrintData(@RequestBody Map<String, Object> params) {
        try {
            String templateId = (String) params.get("templateId");
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            String baseTemplateId = (String) params.get("baseTemplateId");
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return fail("彩签模板ID不能为空");
            }
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            Map<String, Object> result = colorLabelService.generatePrintData(templateId, coinIds, baseTemplateId);
            return success(result);
        } catch (Exception e) {
            return fail("生成打印数据失败：" + e.getMessage());
        }
    }

    /**
     * 上传彩签图片素材
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:upload')")
    @OperationLog
    @PostMapping("/upload-image")
    public ApiResult<?> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return fail("请选择要上传的文件");
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return fail("只能上传图片文件");
            }
            
            // 验证文件大小 (5MB)
            if (file.getSize() > 5 * 1024 * 1024) {
                return fail("图片文件不能超过5MB");
            }
            
            Map<String, Object> result = colorLabelService.uploadImage(file);
            return success(result);
        } catch (Exception e) {
            return fail("上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用的数据字段列表
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/data-fields")
    public ApiResult<?> getDataFields() {
        try {
            List<Map<String, Object>> result = colorLabelService.getAvailableDataFields();
            return success(result);
        } catch (Exception e) {
            return fail("获取数据字段失败：" + e.getMessage());
        }
    }

    /**
     * 验证模板配置
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:save')")
    @OperationLog
    @PostMapping("/validate-config")
    public ApiResult<?> validateConfig(@RequestBody Map<String, Object> templateConfig) {
        try {
            Map<String, Object> result = colorLabelService.validateTemplateConfig(templateConfig);
            return success(result);
        } catch (Exception e) {
            return fail("验证失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板使用统计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/templates/{id}/statistics")
    public ApiResult<?> getTemplateStatistics(@PathVariable("id") String id) {
        try {
            Map<String, Object> result = colorLabelService.getTemplateStatistics(id);
            return success(result);
        } catch (Exception e) {
            return fail("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:delete')")
    @OperationLog
    @PostMapping("/templates/batch-delete")
    public ApiResult<?> batchDeleteTemplates(@RequestBody List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return fail("请选择要删除的模板");
            }
            
            int deletedCount = colorLabelService.batchDeleteTemplates(ids);
            return success("成功删除 " + deletedCount + " 个模板");
        } catch (Exception e) {
            return fail("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 导出彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:export')")
    @OperationLog
    @GetMapping("/templates/{id}/export")
    public ApiResult<?> exportTemplate(@PathVariable("id") String id) {
        try {
            Map<String, Object> result = colorLabelService.exportTemplate(id);
            return success(result);
        } catch (Exception e) {
            return fail("导出失败：" + e.getMessage());
        }
    }

    /**
     * 导入彩签模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:import')")
    @OperationLog
    @PostMapping("/templates/import")
    public ApiResult<?> importTemplate(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return fail("请选择要导入的文件");
            }
            
            ColorLabelTemplateVO result = colorLabelService.importTemplate(file);
            return success(result);
        } catch (Exception e) {
            return fail("导入失败：" + e.getMessage());
        }
    }
}
