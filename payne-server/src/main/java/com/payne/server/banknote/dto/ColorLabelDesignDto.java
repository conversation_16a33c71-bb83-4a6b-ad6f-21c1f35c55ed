package com.payne.server.banknote.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 彩签设计数据传输对象
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ColorLabelDesignDto {
    
    /**
     * 主键ID（更新时需要）
     */
    private String id;
    
    /**
     * 设计名称
     */
    @NotBlank(message = "设计名称不能为空")
    @Size(max = 100, message = "设计名称长度不能超过100个字符")
    private String designName;
    
    /**
     * 基础标签模板ID
     */
    @NotBlank(message = "基础模板ID不能为空")
    private String baseTemplateId;
    
    /**
     * 送评单号
     */
    private String sendformNumber;
    
    /**
     * 关联的钱币ID列表（JSON字符串）
     */
    private String coinIds;
    
    /**
     * 彩签元素配置（JSON字符串）
     */
    private String colorElements;
    
    /**
     * 合成后的完整模板配置（JSON字符串）
     */
    private String composedTemplate;
    
    /**
     * 设计类型：SINGLE-单个钱币，BATCH-批量钱币，TEMPLATE-设计模板
     */
    private String designType;
    
    /**
     * 设计状态：DRAFT-草稿，SAVED-已保存，TEMPLATE-作为模板
     */
    private String status;
    
    /**
     * 预览图片URL
     */
    private String previewImage;
    
    /**
     * 设计描述
     */
    @Size(max = 500, message = "设计描述长度不能超过500个字符")
    private String description;
    
    /**
     * 扩展字段1
     */
    private String extField1;
    
    /**
     * 扩展字段2
     */
    private String extField2;
    
    /**
     * 扩展字段3（JSON格式）
     */
    private String extField3;
    
    // 以下字段用于前端传递结构化数据，后端会转换为JSON字符串存储
    
    /**
     * 钱币ID列表对象（前端传递）
     */
    private List<String> coinIdsObj;
    
    /**
     * 彩签元素配置对象（前端传递）
     */
    private List<ColorElementDto> colorElementsObj;
    
    /**
     * 合成后的模板配置对象（前端传递）
     */
    private Map<String, Object> composedTemplateObj;
    
    /**
     * 钱币数据对象（前端传递，用于预览）
     */
    private List<Map<String, Object>> coinDataObj;
    
    /**
     * 基础模板配置对象（前端传递）
     */
    private Map<String, Object> baseTemplateObj;
    
    /**
     * 彩签元素DTO
     */
    @Data
    public static class ColorElementDto {
        /**
         * 元素ID
         */
        private String id;
        
        /**
         * 元素类型：text, image, qrcode, rectangle, circle
         */
        private String type;
        
        /**
         * 元素名称
         */
        private String name;
        
        /**
         * 位置X坐标
         */
        private Double left;
        
        /**
         * 位置Y坐标
         */
        private Double top;
        
        /**
         * 宽度
         */
        private Double width;
        
        /**
         * 高度
         */
        private Double height;
        
        /**
         * 旋转角度
         */
        private Double angle;
        
        /**
         * 透明度
         */
        private Double opacity;
        
        /**
         * 是否可见
         */
        private Boolean visible;
        
        /**
         * 是否锁定
         */
        private Boolean locked;
        
        /**
         * 数据绑定字段
         */
        private String dataBinding;
        
        /**
         * 元素样式配置
         */
        private Map<String, Object> style;
        
        /**
         * 元素特定属性（如文本内容、图片URL等）
         */
        private Map<String, Object> properties;
        
        /**
         * 层级索引
         */
        private Integer zIndex;
        
        // 文本元素特有属性
        /**
         * 文本内容
         */
        private String text;
        
        /**
         * 字体大小
         */
        private Integer fontSize;
        
        /**
         * 字体族
         */
        private String fontFamily;
        
        /**
         * 字体粗细
         */
        private String fontWeight;
        
        /**
         * 字体样式
         */
        private String fontStyle;
        
        /**
         * 文本颜色
         */
        private String fill;
        
        /**
         * 文本对齐方式
         */
        private String textAlign;
        
        // 图片元素特有属性
        /**
         * 图片源地址
         */
        private String src;
        
        // 形状元素特有属性
        /**
         * 填充颜色
         */
        private String fillColor;
        
        /**
         * 边框颜色
         */
        private String stroke;
        
        /**
         * 边框宽度
         */
        private Integer strokeWidth;
        
        /**
         * 圆形半径（圆形元素专用）
         */
        private Double radius;
    }
    
    /**
     * 验证DTO数据的有效性
     */
    public boolean isValid() {
        if (designName == null || designName.trim().isEmpty()) {
            return false;
        }
        
        if (baseTemplateId == null || baseTemplateId.trim().isEmpty()) {
            return false;
        }
        
        // 可以添加更多验证逻辑
        return true;
    }
    
    /**
     * 获取默认设计类型
     */
    public String getDefaultDesignType() {
        return designType != null ? designType : "BATCH";
    }
    
    /**
     * 获取默认状态
     */
    public String getDefaultStatus() {
        return status != null ? status : "DRAFT";
    }
    
    /**
     * 判断是否为批量设计
     */
    public boolean isBatchDesign() {
        return "BATCH".equals(designType);
    }
    
    /**
     * 判断是否为单个钱币设计
     */
    public boolean isSingleDesign() {
        return "SINGLE".equals(designType);
    }
    
    /**
     * 判断是否为设计模板
     */
    public boolean isTemplate() {
        return "TEMPLATE".equals(designType) || "TEMPLATE".equals(status);
    }
    
    /**
     * 获取钱币数量
     */
    public int getCoinCount() {
        if (coinIdsObj != null) {
            return coinIdsObj.size();
        }
        if (coinDataObj != null) {
            return coinDataObj.size();
        }
        return 0;
    }
    
    /**
     * 获取彩签元素数量
     */
    public int getElementCount() {
        if (colorElementsObj != null) {
            return colorElementsObj.size();
        }
        return 0;
    }
}
