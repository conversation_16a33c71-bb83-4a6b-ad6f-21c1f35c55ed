package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 彩签使用记录实体类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Entity
@Table(name = "COLOR_LABEL_USAGE")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("COLOR_LABEL_USAGE")
public class ColorLabelUsage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 彩签模板ID
     */
    @Column(name = "TEMPLATE_ID", columnDefinition = "VARCHAR2(50)")
    @TableField("TEMPLATE_ID")
    private String templateId;
    
    /**
     * 钱币ID列表（JSON格式）
     */
    @Column(name = "COIN_IDS", columnDefinition = "CLOB")
    @TableField("COIN_IDS")
    private String coinIds;
    
    /**
     * 基础模板ID
     */
    @Column(name = "BASE_TEMPLATE_ID", columnDefinition = "VARCHAR2(50)")
    @TableField("BASE_TEMPLATE_ID")
    private String baseTemplateId;
    
    /**
     * 打印时间
     */
    @Column(name = "PRINT_TIME")
    @TableField("PRINT_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime printTime;
    
    /**
     * 打印数量
     */
    @Column(name = "PRINT_COUNT", columnDefinition = "NUMBER(10)")
    @TableField("PRINT_COUNT")
    private Integer printCount;
    
    /**
     * 创建用户
     */
    @Column(name = "CREATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("CREATE_USER")
    private String createUser;
    
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 扩展字段1
     */
    @Column(name = "EXT_FIELD1", columnDefinition = "VARCHAR2(200)")
    @TableField("EXT_FIELD1")
    private String extField1;
    
    /**
     * 扩展字段2
     */
    @Column(name = "EXT_FIELD2", columnDefinition = "VARCHAR2(200)")
    @TableField("EXT_FIELD2")
    private String extField2;
}
