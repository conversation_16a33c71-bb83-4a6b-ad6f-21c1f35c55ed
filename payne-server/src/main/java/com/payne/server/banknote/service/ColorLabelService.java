package com.payne.server.banknote.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.web.PageParam;
import com.payne.server.banknote.entity.ColorLabelTemplate;
import com.payne.server.banknote.vo.ColorLabelTemplateVO;
import com.payne.server.banknote.dto.ColorLabelTemplateDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 彩签模板服务接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ColorLabelService extends IService<ColorLabelTemplate> {

    /**
     * 分页查询彩签模板列表
     *
     * @param page    分页参数
     * @param wrapper 查询条件
     * @return 分页结果
     */
    IPage<ColorLabelTemplateVO> pageTemplates(PageParam<ColorLabelTemplate, ?> page,
                                              QueryWrapper<ColorLabelTemplate> wrapper);

    /**
     * 根据ID获取彩签模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    ColorLabelTemplateVO getTemplateById(String id);

    /**
     * 保存彩签模板
     *
     * @param templateDto 模板数据传输对象
     * @return 保存后的模板视图对象
     */
    ColorLabelTemplateVO saveTemplate(ColorLabelTemplateDto templateDto);

    /**
     * 更新彩签模板
     *
     * @param templateDto 模板数据传输对象
     * @return 更新后的模板视图对象
     */
    ColorLabelTemplateVO updateTemplate(ColorLabelTemplateDto templateDto);

    /**
     * 删除彩签模板
     *
     * @param id 模板ID
     * @return 是否删除成功
     */
    boolean deleteTemplate(String id);

    /**
     * 复制彩签模板
     *
     * @param sourceId 源模板ID
     * @param newName  新模板名称
     * @return 复制后的模板视图对象
     */
    ColorLabelTemplateVO copyTemplate(String sourceId, String newName);

    /**
     * 预览彩签模板
     *
     * @param templateId 模板ID
     * @param coinIds    钱币ID列表
     * @return 预览数据
     */
    Map<String, Object> previewTemplate(String templateId, List<String> coinIds);

    /**
     * 生成彩签批量打印数据
     *
     * @param templateId     彩签模板ID
     * @param coinIds        钱币ID列表
     * @param baseTemplateId 基础模板ID
     * @return 打印数据
     */
    Map<String, Object> generatePrintData(String templateId, List<String> coinIds, String baseTemplateId);

    /**
     * 上传彩签图片素材
     *
     * @param file 图片文件
     * @return 上传结果，包含图片URL等信息
     */
    Map<String, Object> uploadImage(MultipartFile file);

    /**
     * 获取可用的数据字段列表
     *
     * @return 数据字段列表
     */
    List<Map<String, Object>> getAvailableDataFields();

    /**
     * 验证模板配置
     *
     * @param templateConfig 模板配置
     * @return 验证结果
     */
    Map<String, Object> validateTemplateConfig(Map<String, Object> templateConfig);

    /**
     * 获取模板使用统计
     *
     * @param templateId 模板ID
     * @return 统计信息
     */
    Map<String, Object> getTemplateStatistics(String templateId);

    /**
     * 批量删除彩签模板
     *
     * @param ids 模板ID列表
     * @return 删除的数量
     */
    int batchDeleteTemplates(List<String> ids);

    /**
     * 导出彩签模板
     *
     * @param id 模板ID
     * @return 导出数据
     */
    Map<String, Object> exportTemplate(String id);

    /**
     * 导入彩签模板
     *
     * @param file 模板文件
     * @return 导入后的模板视图对象
     */
    ColorLabelTemplateVO importTemplate(MultipartFile file);

    /**
     * 根据模板类型获取模板列表
     *
     * @param templateType 模板类型
     * @return 模板列表
     */
    List<ColorLabelTemplateVO> getTemplatesByType(String templateType);

    /**
     * 检查模板名称是否已存在
     *
     * @param templateName 模板名称
     * @param excludeId    排除的模板ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isTemplateNameExists(String templateName, String excludeId);

    /**
     * 获取默认模板
     *
     * @return 默认模板
     */
    ColorLabelTemplateVO getDefaultTemplate();

    /**
     * 设置默认模板
     *
     * @param templateId 模板ID
     * @return 是否设置成功
     */
    boolean setDefaultTemplate(String templateId);

    /**
     * 获取模板预览图
     *
     * @param templateId 模板ID
     * @return 预览图URL
     */
    String getTemplatePreviewImage(String templateId);

    /**
     * 生成模板预览图
     *
     * @param templateId 模板ID
     * @return 预览图URL
     */
    String generateTemplatePreviewImage(String templateId);

    /**
     * 合成彩签和基础模板
     *
     * @param colorLabelData 彩签数据
     * @param baseTemplateData 基础模板数据
     * @param coinData 钱币数据
     * @return 合成后的HTML内容
     */
    String composeTemplate(Map<String, Object> colorLabelData, 
                          Map<String, Object> baseTemplateData, 
                          Map<String, Object> coinData);

    /**
     * 转换坐标系统
     *
     * @param designCoordinates 设计坐标
     * @param canvasSize 画布尺寸
     * @param labelSize 标签尺寸
     * @return 实际坐标
     */
    Map<String, Object> convertCoordinates(Map<String, Object> designCoordinates,
                                          Map<String, Object> canvasSize,
                                          Map<String, Object> labelSize);

    /**
     * 渲染彩签元素为HTML
     *
     * @param element 元素配置
     * @param coinData 钱币数据
     * @return HTML字符串
     */
    String renderElementToHtml(Map<String, Object> element, Map<String, Object> coinData);

    /**
     * 处理数据绑定模板
     *
     * @param template 模板字符串
     * @param data 数据对象
     * @return 处理后的字符串
     */
    String processDataBinding(String template, Map<String, Object> data);
}
