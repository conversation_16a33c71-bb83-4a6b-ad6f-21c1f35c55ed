package com.payne.server.banknote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.server.banknote.entity.ColorLabelUsage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 彩签使用记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface ColorLabelUsageMapper extends BaseMapper<ColorLabelUsage> {

    /**
     * 根据模板ID查询使用记录
     *
     * @param templateId 模板ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM COLOR_LABEL_USAGE WHERE TEMPLATE_ID = #{templateId} " +
            "ORDER BY PRINT_TIME DESC")
    List<ColorLabelUsage> selectByTemplateId(@Param("templateId") String templateId);

    /**
     * 统计模板使用次数
     *
     * @param templateId 模板ID
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM COLOR_LABEL_USAGE WHERE TEMPLATE_ID = #{templateId}")
    int countByTemplateId(@Param("templateId") String templateId);

    /**
     * 统计模板打印总数量
     *
     * @param templateId 模板ID
     * @return 打印总数量
     */
    @Select("SELECT COALESCE(SUM(PRINT_COUNT), 0) FROM COLOR_LABEL_USAGE " +
            "WHERE TEMPLATE_ID = #{templateId}")
    int sumPrintCountByTemplateId(@Param("templateId") String templateId);

    /**
     * 查询用户的使用记录
     *
     * @param createUser 创建用户
     * @param limit 限制数量
     * @return 使用记录列表
     */
    @Select("SELECT * FROM COLOR_LABEL_USAGE WHERE CREATE_USER = #{createUser} " +
            "ORDER BY PRINT_TIME DESC LIMIT #{limit}")
    List<ColorLabelUsage> selectByCreateUser(@Param("createUser") String createUser, 
                                           @Param("limit") Integer limit);

    /**
     * 查询时间范围内的使用记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 使用记录列表
     */
    @Select("SELECT * FROM COLOR_LABEL_USAGE " +
            "WHERE PRINT_TIME >= #{startTime} AND PRINT_TIME <= #{endTime} " +
            "ORDER BY PRINT_TIME DESC")
    List<ColorLabelUsage> selectByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                          @Param("endTime") LocalDateTime endTime);

    /**
     * 统计每日使用情况
     *
     * @param days 天数
     * @return 每日统计数据
     */
    @Select("SELECT " +
            "DATE_FORMAT(PRINT_TIME, '%Y-%m-%d') as date, " +
            "COUNT(*) as usageCount, " +
            "SUM(PRINT_COUNT) as printCount " +
            "FROM COLOR_LABEL_USAGE " +
            "WHERE PRINT_TIME >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE_FORMAT(PRINT_TIME, '%Y-%m-%d') " +
            "ORDER BY date DESC")
    List<Map<String, Object>> selectDailyStatistics(@Param("days") Integer days);

    /**
     * 统计模板使用排行
     *
     * @param limit 限制数量
     * @return 使用排行数据
     */
    @Select("SELECT " +
            "u.TEMPLATE_ID as templateId, " +
            "t.TEMPLATE_NAME as templateName, " +
            "COUNT(*) as usageCount, " +
            "SUM(u.PRINT_COUNT) as totalPrintCount, " +
            "MAX(u.PRINT_TIME) as lastUsedTime " +
            "FROM COLOR_LABEL_USAGE u " +
            "LEFT JOIN COLOR_LABEL_TEMPLATE t ON u.TEMPLATE_ID = t.ID " +
            "WHERE t.DELETED = 0 " +
            "GROUP BY u.TEMPLATE_ID, t.TEMPLATE_NAME " +
            "ORDER BY usageCount DESC, totalPrintCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectTemplateUsageRanking(@Param("limit") Integer limit);

    /**
     * 查询最近使用的模板
     *
     * @param createUser 创建用户
     * @param limit 限制数量
     * @return 最近使用的模板
     */
    @Select("SELECT DISTINCT " +
            "u.TEMPLATE_ID as templateId, " +
            "t.TEMPLATE_NAME as templateName, " +
            "MAX(u.PRINT_TIME) as lastUsedTime " +
            "FROM COLOR_LABEL_USAGE u " +
            "LEFT JOIN COLOR_LABEL_TEMPLATE t ON u.TEMPLATE_ID = t.ID " +
            "WHERE u.CREATE_USER = #{createUser} AND t.DELETED = 0 " +
            "GROUP BY u.TEMPLATE_ID, t.TEMPLATE_NAME " +
            "ORDER BY lastUsedTime DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectRecentUsedTemplates(@Param("createUser") String createUser, 
                                                       @Param("limit") Integer limit);
}
