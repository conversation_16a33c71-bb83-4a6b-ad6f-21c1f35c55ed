package com.payne.server.banknote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.server.banknote.entity.ColorLabelTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 彩签模板Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface ColorLabelTemplateMapper extends BaseMapper<ColorLabelTemplate> {

    /**
     * 根据模板类型查询模板列表
     *
     * @param templateType 模板类型
     * @param status 模板状态
     * @return 模板列表
     */
    @Select("SELECT * FROM COLOR_LABEL_TEMPLATE WHERE TEMPLATE_TYPE = #{templateType} " +
            "AND STATUS = #{status} AND DELETED = 0 ORDER BY CREATE_TIME DESC")
    List<ColorLabelTemplate> selectByTypeAndStatus(@Param("templateType") String templateType, 
                                                   @Param("status") String status);

    /**
     * 查询默认模板
     *
     * @return 默认模板
     */
    @Select("SELECT * FROM COLOR_LABEL_TEMPLATE WHERE IS_DEFAULT = 1 " +
            "AND STATUS = 'ACTIVE' AND DELETED = 0 LIMIT 1")
    ColorLabelTemplate selectDefaultTemplate();

    /**
     * 清除所有默认模板标记
     *
     * @return 更新行数
     */
    @Update("UPDATE COLOR_LABEL_TEMPLATE SET IS_DEFAULT = 0 WHERE IS_DEFAULT = 1 AND DELETED = 0")
    int clearAllDefaultFlags();

    /**
     * 更新模板使用次数
     *
     * @param templateId 模板ID
     * @param lastUsedTime 最后使用时间
     * @return 更新行数
     */
    @Update("UPDATE COLOR_LABEL_TEMPLATE SET USE_COUNT = COALESCE(USE_COUNT, 0) + 1, " +
            "LAST_USED_TIME = #{lastUsedTime} WHERE ID = #{templateId} AND DELETED = 0")
    int updateUsageCount(@Param("templateId") String templateId, 
                        @Param("lastUsedTime") LocalDateTime lastUsedTime);

    /**
     * 根据关键词搜索模板
     *
     * @param keyword 关键词
     * @param createUser 创建用户（可选）
     * @return 模板列表
     */
    @Select("<script>" +
            "SELECT * FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (TEMPLATE_NAME LIKE CONCAT('%', #{keyword}, '%') " +
            "OR DESCRIPTION LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='createUser != null and createUser != \"\"'>" +
            "AND CREATE_USER = #{createUser} " +
            "</if>" +
            "ORDER BY CREATE_TIME DESC" +
            "</script>")
    List<ColorLabelTemplate> searchTemplates(@Param("keyword") String keyword, 
                                           @Param("createUser") String createUser);

    /**
     * 查询热门模板（使用次数较多）
     *
     * @param minUseCount 最小使用次数
     * @param limit 限制数量
     * @return 模板列表
     */
    @Select("SELECT * FROM COLOR_LABEL_TEMPLATE WHERE USE_COUNT >= #{minUseCount} " +
            "AND STATUS = 'ACTIVE' AND DELETED = 0 " +
            "ORDER BY USE_COUNT DESC, LAST_USED_TIME DESC LIMIT #{limit}")
    List<ColorLabelTemplate> selectPopularTemplates(@Param("minUseCount") Integer minUseCount, 
                                                   @Param("limit") Integer limit);

    /**
     * 查询最近创建的模板
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 模板列表
     */
    @Select("SELECT * FROM COLOR_LABEL_TEMPLATE WHERE CREATE_TIME >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND DELETED = 0 ORDER BY CREATE_TIME DESC LIMIT #{limit}")
    List<ColorLabelTemplate> selectRecentTemplates(@Param("days") Integer days, 
                                                  @Param("limit") Integer limit);

    /**
     * 统计模板使用情况
     *
     * @param templateId 模板ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "t.ID as templateId, " +
            "t.TEMPLATE_NAME as templateName, " +
            "t.USE_COUNT as useCount, " +
            "t.LAST_USED_TIME as lastUsedTime, " +
            "t.CREATE_TIME as createTime, " +
            "t.UPDATE_TIME as updateTime " +
            "FROM COLOR_LABEL_TEMPLATE t " +
            "WHERE t.ID = #{templateId} AND t.DELETED = 0")
    Map<String, Object> selectTemplateStatistics(@Param("templateId") String templateId);

    /**
     * 查询模板使用趋势（按月统计）
     *
     * @param templateId 模板ID
     * @param months 月数
     * @return 使用趋势数据
     */
    @Select("<script>" +
            "SELECT " +
            "DATE_FORMAT(LAST_USED_TIME, '%Y-%m') as month, " +
            "COUNT(*) as usageCount " +
            "FROM COLOR_LABEL_TEMPLATE " +
            "WHERE ID = #{templateId} " +
            "AND LAST_USED_TIME >= DATE_SUB(NOW(), INTERVAL #{months} MONTH) " +
            "AND DELETED = 0 " +
            "GROUP BY DATE_FORMAT(LAST_USED_TIME, '%Y-%m') " +
            "ORDER BY month DESC" +
            "</script>")
    List<Map<String, Object>> selectUsageTrend(@Param("templateId") String templateId, 
                                              @Param("months") Integer months);

    /**
     * 批量更新模板状态
     *
     * @param ids 模板ID列表
     * @param status 新状态
     * @param updateUser 更新用户
     * @param updateTime 更新时间
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE COLOR_LABEL_TEMPLATE SET " +
            "STATUS = #{status}, " +
            "UPDATE_USER = #{updateUser}, " +
            "UPDATE_TIME = #{updateTime} " +
            "WHERE ID IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "AND DELETED = 0" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, 
                         @Param("status") String status,
                         @Param("updateUser") String updateUser, 
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询模板分类统计
     *
     * @return 分类统计信息
     */
    @Select("SELECT " +
            "TEMPLATE_TYPE as templateType, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN STATUS = 'ACTIVE' THEN 1 ELSE 0 END) as activeCount, " +
            "SUM(COALESCE(USE_COUNT, 0)) as totalUseCount " +
            "FROM COLOR_LABEL_TEMPLATE " +
            "WHERE DELETED = 0 " +
            "GROUP BY TEMPLATE_TYPE " +
            "ORDER BY count DESC")
    List<Map<String, Object>> selectCategoryStatistics();

    /**
     * 检查模板名称是否存在
     *
     * @param templateName 模板名称
     * @param excludeId 排除的ID
     * @return 存在的数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM COLOR_LABEL_TEMPLATE " +
            "WHERE TEMPLATE_NAME = #{templateName} AND DELETED = 0 " +
            "<if test='excludeId != null and excludeId != \"\"'>" +
            "AND ID != #{excludeId}" +
            "</if>" +
            "</script>")
    int countByTemplateName(@Param("templateName") String templateName, 
                           @Param("excludeId") String excludeId);

    /**
     * 查询用户的模板数量
     *
     * @param createUser 创建用户
     * @return 模板数量
     */
    @Select("SELECT COUNT(*) FROM COLOR_LABEL_TEMPLATE " +
            "WHERE CREATE_USER = #{createUser} AND DELETED = 0")
    int countByCreateUser(@Param("createUser") String createUser);

    /**
     * 清理长期未使用的模板（可选功能）
     *
     * @param days 天数
     * @return 清理的数量
     */
    @Update("UPDATE COLOR_LABEL_TEMPLATE SET STATUS = 'INACTIVE' " +
            "WHERE (LAST_USED_TIME IS NULL OR LAST_USED_TIME < DATE_SUB(NOW(), INTERVAL #{days} DAY)) " +
            "AND STATUS = 'ACTIVE' AND IS_DEFAULT = 0 AND DELETED = 0")
    int markInactiveTemplates(@Param("days") Integer days);
}
