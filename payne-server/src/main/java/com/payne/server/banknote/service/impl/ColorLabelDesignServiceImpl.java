package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.payne.server.banknote.entity.ColorLabelDesign;
import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.service.ColorLabelDesignService;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.vo.ColorLabelDesignVO;
import com.payne.server.banknote.dto.ColorLabelDesignDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 彩签设计服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ColorLabelDesignServiceImpl extends ServiceImpl<ColorLabelDesignMapper, ColorLabelDesign> 
        implements ColorLabelDesignService {

    private final ObjectMapper objectMapper;
    private final PjOSendformItemService pjOSendformItemService;
    private final LabelTemplateService labelTemplateService;

    @Override
    public ColorLabelDesignVO createColorLabelBySendform(String sendformNumber, String templateId) {
        try {
            // 1. 获取送评单下的钱币数据
            List<PjOSendformItem> coins = pjOSendformItemService.getBySendformNumber(sendformNumber);
            if (coins.isEmpty()) {
                throw new RuntimeException("送评单下没有找到钱币数据");
            }

            // 2. 获取基础标签模板
            LabelTemplate baseTemplate = labelTemplateService.getById(templateId);
            if (baseTemplate == null) {
                throw new RuntimeException("基础模板不存在");
            }

            // 3. 创建彩签设计
            ColorLabelDesign design = new ColorLabelDesign();
            design.setDesignName("送评单" + sendformNumber + "的彩签设计");
            design.setBaseTemplateId(templateId);
            design.setSendformNumber(sendformNumber);
            design.setDesignType("BATCH");
            design.setStatus("DRAFT");
            design.setCreateUser(SecurityUtil.getLoginUsername());
            design.setCreateTime(LocalDateTime.now());

            // 4. 设置钱币ID列表
            List<String> coinIds = coins.stream()
                    .map(PjOSendformItem::getId)
                    .collect(Collectors.toList());
            design.setCoinIds(objectMapper.writeValueAsString(coinIds));

            // 5. 初始化空的彩签元素
            design.setColorElements("[]");

            // 6. 保存设计
            this.save(design);

            return convertToVO(design, baseTemplate, coins);

        } catch (Exception e) {
            log.error("根据送评单号创建彩签设计失败", e);
            throw new RuntimeException("创建彩签设计失败: " + e.getMessage());
        }
    }

    @Override
    public ColorLabelDesignVO createColorLabelByDiyCode(String diyCode, String templateId) {
        try {
            // 1. 根据送评条码获取钱币数据
            PjOSendformItem coin = pjOSendformItemService.getByDiyCode(diyCode);
            if (coin == null) {
                throw new RuntimeException("未找到对应的钱币数据");
            }

            // 2. 获取基础标签模板
            LabelTemplate baseTemplate = labelTemplateService.getById(templateId);
            if (baseTemplate == null) {
                throw new RuntimeException("基础模板不存在");
            }

            // 3. 创建彩签设计
            ColorLabelDesign design = new ColorLabelDesign();
            design.setDesignName("钱币" + coin.getSerialNumber() + "的彩签设计");
            design.setBaseTemplateId(templateId);
            design.setSendformNumber(coin.getSendformNumber());
            design.setDesignType("SINGLE");
            design.setStatus("DRAFT");
            design.setCreateUser(SecurityUtil.getLoginUsername());
            design.setCreateTime(LocalDateTime.now());

            // 4. 设置钱币ID
            List<String> coinIds = Collections.singletonList(coin.getId());
            design.setCoinIds(objectMapper.writeValueAsString(coinIds));

            // 5. 初始化空的彩签元素
            design.setColorElements("[]");

            // 6. 保存设计
            this.save(design);

            return convertToVO(design, baseTemplate, Collections.singletonList(coin));

        } catch (Exception e) {
            log.error("根据送评条码创建彩签设计失败", e);
            throw new RuntimeException("创建彩签设计失败: " + e.getMessage());
        }
    }

    @Override
    public ColorLabelDesignVO createColorLabelByCoins(List<String> coinIds, String templateId) {
        try {
            // 1. 获取钱币数据
            List<PjOSendformItem> coins = pjOSendformItemService.listByIds(coinIds);
            if (coins.isEmpty()) {
                throw new RuntimeException("未找到钱币数据");
            }

            // 2. 获取基础标签模板
            LabelTemplate baseTemplate = labelTemplateService.getById(templateId);
            if (baseTemplate == null) {
                throw new RuntimeException("基础模板不存在");
            }

            // 3. 创建彩签设计
            ColorLabelDesign design = new ColorLabelDesign();
            design.setDesignName("选定钱币的彩签设计");
            design.setBaseTemplateId(templateId);
            design.setDesignType(coinIds.size() > 1 ? "BATCH" : "SINGLE");
            design.setStatus("DRAFT");
            design.setCreateUser(SecurityUtil.getLoginUsername());
            design.setCreateTime(LocalDateTime.now());

            // 4. 设置钱币ID列表
            design.setCoinIds(objectMapper.writeValueAsString(coinIds));

            // 5. 初始化空的彩签元素
            design.setColorElements("[]");

            // 6. 保存设计
            this.save(design);

            return convertToVO(design, baseTemplate, coins);

        } catch (Exception e) {
            log.error("根据钱币ID列表创建彩签设计失败", e);
            throw new RuntimeException("创建彩签设计失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ColorLabelDesignVO saveColorLabelDesign(ColorLabelDesignDto designDto) {
        try {
            ColorLabelDesign design;
            
            if (StringUtils.hasText(designDto.getId())) {
                // 更新现有设计
                design = this.getById(designDto.getId());
                if (design == null) {
                    throw new RuntimeException("设计不存在");
                }
                design.setUpdateUser(SecurityUtil.getLoginUsername());
                design.setUpdateTime(LocalDateTime.now());
            } else {
                // 创建新设计
                design = new ColorLabelDesign();
                design.setCreateUser(SecurityUtil.getLoginUsername());
                design.setCreateTime(LocalDateTime.now());
            }

            // 复制基础属性
            BeanUtils.copyProperties(designDto, design, "id", "createUser", "createTime");

            // 处理JSON字段
            processJsonFields(designDto, design);

            // 保存或更新
            this.saveOrUpdate(design);

            // 获取相关数据并返回VO
            LabelTemplate baseTemplate = labelTemplateService.getById(design.getBaseTemplateId());
            List<String> coinIds = parseCoinIds(design.getCoinIds());
            List<PjOSendformItem> coins = coinIds.isEmpty() ? 
                Collections.emptyList() : pjOSendformItemService.listByIds(coinIds);

            return convertToVO(design, baseTemplate, coins);

        } catch (Exception e) {
            log.error("保存彩签设计失败", e);
            throw new RuntimeException("保存彩签设计失败: " + e.getMessage());
        }
    }

    @Override
    public ColorLabelDesignVO getColorLabelDesign(String designId) {
        try {
            ColorLabelDesign design = this.getById(designId);
            if (design == null) {
                return null;
            }

            // 获取相关数据
            LabelTemplate baseTemplate = labelTemplateService.getById(design.getBaseTemplateId());
            List<String> coinIds = parseCoinIds(design.getCoinIds());
            List<PjOSendformItem> coins = coinIds.isEmpty() ? 
                Collections.emptyList() : pjOSendformItemService.listByIds(coinIds);

            return convertToVO(design, baseTemplate, coins);

        } catch (Exception e) {
            log.error("获取彩签设计失败", e);
            throw new RuntimeException("获取彩签设计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> previewColorLabelDesign(ColorLabelDesignDto designDto) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取基础模板
            LabelTemplate baseTemplate = labelTemplateService.getById(designDto.getBaseTemplateId());
            if (baseTemplate == null) {
                throw new RuntimeException("基础模板不存在");
            }

            // 获取钱币数据
            List<String> coinIds = designDto.getCoinIdsObj();
            List<PjOSendformItem> coins = coinIds != null && !coinIds.isEmpty() ? 
                pjOSendformItemService.listByIds(coinIds) : Collections.emptyList();

            // 生成预览数据
            List<Map<String, Object>> previewItems = generatePreviewItems(designDto, coins);

            result.put("baseTemplate", baseTemplate);
            result.put("coinData", coins);
            result.put("previewItems", previewItems);
            result.put("totalCount", coins.size());

            return result;

        } catch (Exception e) {
            log.error("预览彩签设计失败", e);
            throw new RuntimeException("预览彩签设计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> generateColorLabelPrintData(String designId) {
        try {
            ColorLabelDesign design = this.getById(designId);
            if (design == null) {
                throw new RuntimeException("设计不存在");
            }

            // 获取相关数据
            LabelTemplate baseTemplate = labelTemplateService.getById(design.getBaseTemplateId());
            List<String> coinIds = parseCoinIds(design.getCoinIds());
            List<PjOSendformItem> coins = pjOSendformItemService.listByIds(coinIds);

            // 更新使用次数
            updateUsageCount(designId);

            Map<String, Object> result = new HashMap<>();
            result.put("design", convertToVO(design, baseTemplate, coins));
            result.put("baseTemplate", baseTemplate);
            result.put("coinData", coins);
            result.put("printItems", generatePrintItems(design, coins));
            result.put("totalCount", coins.size());

            return result;

        } catch (Exception e) {
            log.error("生成彩签打印数据失败", e);
            throw new RuntimeException("生成彩签打印数据失败: " + e.getMessage());
        }
    }

    // 其他接口方法的简单实现
    @Override
    public Map<String, Object> convertLabelTemplateToColorLabel(LabelTemplate labelTemplate, 
                                                              List<PjOSendformItem> coinData) {
        Map<String, Object> result = new HashMap<>();
        result.put("baseTemplate", labelTemplate);
        result.put("coinData", coinData);
        return result;
    }

    @Override
    public Map<String, Object> composeColorLabelElements(Map<String, Object> baseTemplate,
                                                       List<Map<String, Object>> colorElements,
                                                       Map<String, Object> coinData) {
        Map<String, Object> result = new HashMap<>();
        result.put("baseTemplate", baseTemplate);
        result.put("colorElements", colorElements);
        result.put("coinData", coinData);
        return result;
    }

    @Override
    public List<Map<String, Object>> getAvailableColorLabelElements() {
        List<Map<String, Object>> elements = new ArrayList<>();
        
        // 文本元素
        Map<String, Object> textElement = new HashMap<>();
        textElement.put("type", "text");
        textElement.put("name", "文本");
        textElement.put("icon", "el-icon-edit-pen");
        elements.add(textElement);
        
        // 图片元素
        Map<String, Object> imageElement = new HashMap<>();
        imageElement.put("type", "image");
        imageElement.put("name", "图片");
        imageElement.put("icon", "el-icon-picture");
        elements.add(imageElement);
        
        // 二维码元素
        Map<String, Object> qrcodeElement = new HashMap<>();
        qrcodeElement.put("type", "qrcode");
        qrcodeElement.put("name", "二维码");
        qrcodeElement.put("icon", "el-icon-grid");
        elements.add(qrcodeElement);
        
        return elements;
    }

    @Override
    public Map<String, Object> validateColorLabelDesign(Map<String, Object> designConfig) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 简单验证逻辑
        if (!designConfig.containsKey("baseTemplateId")) {
            errors.add("缺少基础模板ID");
        }
        
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }

    @Override
    public List<ColorLabelDesignVO> getColorLabelDesignHistory(String sendformNumber) {
        // 简单实现，返回空列表
        return Collections.emptyList();
    }

    @Override
    public ColorLabelDesignVO copyColorLabelDesign(String sourceDesignId, String newName) {
        // 简单实现，抛出未实现异常
        throw new RuntimeException("复制功能暂未实现");
    }

    @Override
    public boolean deleteColorLabelDesign(String designId) {
        return this.removeById(designId);
    }

    @Override
    public List<ColorLabelDesignVO> getColorLabelDesignTemplates() {
        return Collections.emptyList();
    }

    @Override
    public ColorLabelDesignVO applyColorLabelDesignTemplate(String templateDesignId, List<String> coinIds) {
        throw new RuntimeException("应用模板功能暂未实现");
    }

    // 私有辅助方法
    private void processJsonFields(ColorLabelDesignDto dto, ColorLabelDesign entity) {
        try {
            // 处理钱币ID列表
            if (dto.getCoinIdsObj() != null) {
                entity.setCoinIds(objectMapper.writeValueAsString(dto.getCoinIdsObj()));
            } else if (StringUtils.hasText(dto.getCoinIds())) {
                entity.setCoinIds(dto.getCoinIds());
            }
            
            // 处理彩签元素
            if (dto.getColorElementsObj() != null) {
                entity.setColorElements(objectMapper.writeValueAsString(dto.getColorElementsObj()));
            } else if (StringUtils.hasText(dto.getColorElements())) {
                entity.setColorElements(dto.getColorElements());
            }
            
            // 处理合成模板
            if (dto.getComposedTemplateObj() != null) {
                entity.setComposedTemplate(objectMapper.writeValueAsString(dto.getComposedTemplateObj()));
            } else if (StringUtils.hasText(dto.getComposedTemplate())) {
                entity.setComposedTemplate(dto.getComposedTemplate());
            }
            
        } catch (Exception e) {
            log.error("处理JSON字段失败", e);
            throw new RuntimeException("处理JSON字段失败: " + e.getMessage());
        }
    }

    private ColorLabelDesignVO convertToVO(ColorLabelDesign entity, LabelTemplate baseTemplate, 
                                         List<PjOSendformItem> coins) {
        ColorLabelDesignVO vo = new ColorLabelDesignVO();
        BeanUtils.copyProperties(entity, vo);
        
        // 设置基础模板信息
        if (baseTemplate != null) {
            vo.setBaseTemplateName(baseTemplate.getTemplateName());
        }
        
        // 设置钱币数据
        if (coins != null && !coins.isEmpty()) {
            List<Map<String, Object>> coinData = coins.stream()
                    .map(this::convertCoinToMap)
                    .collect(Collectors.toList());
            vo.setCoinData(coinData);
            vo.setCoinCount(coins.size());
        }
        
        // 解析JSON字段
        try {
            if (StringUtils.hasText(entity.getCoinIds())) {
                vo.setCoinIds(objectMapper.readValue(entity.getCoinIds(), 
                    new TypeReference<List<String>>() {}));
            }
            
            if (StringUtils.hasText(entity.getColorElements())) {
                vo.setColorElements(objectMapper.readValue(entity.getColorElements(), 
                    new TypeReference<List<Map<String, Object>>>() {}));
            }
            
        } catch (Exception e) {
            log.error("解析JSON字段失败", e);
        }
        
        return vo;
    }

    private List<String> parseCoinIds(String coinIdsJson) {
        try {
            if (StringUtils.hasText(coinIdsJson)) {
                return objectMapper.readValue(coinIdsJson, new TypeReference<List<String>>() {});
            }
        } catch (Exception e) {
            log.error("解析钱币ID列表失败", e);
        }
        return Collections.emptyList();
    }

    private Map<String, Object> convertCoinToMap(PjOSendformItem coin) {
        Map<String, Object> coinData = new HashMap<>();
        coinData.put("id", coin.getId());
        coinData.put("serialNumber", coin.getSerialNumber());
        coinData.put("itemName", coin.getItemName());
        coinData.put("gradeLevel", coin.getGradeLevel());
        coinData.put("customerName", coin.getCustomerName());
        coinData.put("diyCode", coin.getDiyCode());
        return coinData;
    }

    private List<Map<String, Object>> generatePreviewItems(ColorLabelDesignDto designDto, 
                                                          List<PjOSendformItem> coins) {
        List<Map<String, Object>> previewItems = new ArrayList<>();
        
        for (PjOSendformItem coin : coins) {
            Map<String, Object> coinData = convertCoinToMap(coin);
            Map<String, Object> previewItem = new HashMap<>();
            previewItem.put("coinId", coin.getId());
            previewItem.put("coinData", coinData);
            previewItem.put("renderedHtml", "预览HTML内容"); // 这里应该实现实际的渲染逻辑
            previewItems.add(previewItem);
        }
        
        return previewItems;
    }

    private List<Map<String, Object>> generatePrintItems(ColorLabelDesign design, 
                                                        List<PjOSendformItem> coins) {
        return generatePreviewItems(null, coins); // 简单实现
    }

    private void updateUsageCount(String designId) {
        ColorLabelDesign design = this.getById(designId);
        if (design != null) {
            design.setUseCount((design.getUseCount() != null ? design.getUseCount() : 0) + 1);
            design.setLastUsedTime(LocalDateTime.now());
            this.updateById(design);
        }
    }
}
