package com.payne.server.banknote.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 彩签模板视图对象
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ColorLabelTemplateVO {
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 模板类型
     */
    private String templateType;
    
    /**
     * 画布配置（解析后的对象）
     */
    private Map<String, Object> canvasConfig;
    
    /**
     * 画布数据（解析后的对象）
     */
    private Map<String, Object> canvasData;
    
    /**
     * 元素列表（解析后的对象）
     */
    private List<Map<String, Object>> elements;
    
    /**
     * 预览图片URL
     */
    private String previewImage;
    
    /**
     * 是否为默认模板
     */
    private Boolean isDefault;
    
    /**
     * 模板状态
     */
    private String status;
    
    /**
     * 模板状态显示名称
     */
    private String statusName;
    
    /**
     * 模板描述
     */
    private String description;
    
    /**
     * 使用次数
     */
    private Integer useCount;
    
    /**
     * 最后使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUsedTime;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 创建用户名称
     */
    private String createUserName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新用户
     */
    private String updateUser;
    
    /**
     * 更新用户名称
     */
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 画布尺寸信息
     */
    private CanvasSize canvasSize;
    
    /**
     * 元素统计信息
     */
    private ElementStatistics elementStatistics;
    
    /**
     * 数据绑定字段列表
     */
    private List<String> dataBindingFields;
    
    /**
     * 是否可编辑（基于权限和状态判断）
     */
    private Boolean editable;
    
    /**
     * 是否可删除（基于权限和使用情况判断）
     */
    private Boolean deletable;
    
    /**
     * 标签信息（用于分类和搜索）
     */
    private List<String> tags;
    
    /**
     * 画布尺寸信息
     */
    @Data
    public static class CanvasSize {
        /**
         * 宽度（像素）
         */
        private Integer width;
        
        /**
         * 高度（像素）
         */
        private Integer height;
        
        /**
         * 实际宽度（毫米）
         */
        private Double actualWidth;
        
        /**
         * 实际高度（毫米）
         */
        private Double actualHeight;
        
        /**
         * 缩放比例
         */
        private Double scale;
    }
    
    /**
     * 元素统计信息
     */
    @Data
    public static class ElementStatistics {
        /**
         * 总元素数量
         */
        private Integer totalCount;
        
        /**
         * 文本元素数量
         */
        private Integer textCount;
        
        /**
         * 图片元素数量
         */
        private Integer imageCount;
        
        /**
         * 二维码元素数量
         */
        private Integer qrcodeCount;
        
        /**
         * 形状元素数量
         */
        private Integer shapeCount;
        
        /**
         * 数据绑定元素数量
         */
        private Integer dataBoundCount;
    }
    
    /**
     * 获取模板类型显示名称
     */
    public String getTemplateTypeName() {
        if (templateType == null) {
            return "未知";
        }
        
        switch (templateType) {
            case "COLOR_LABEL":
                return "彩签模板";
            case "LOGO_TEMPLATE":
                return "Logo模板";
            case "TEXT_TEMPLATE":
                return "文本模板";
            default:
                return templateType;
        }
    }
    
    /**
     * 获取状态显示名称
     */
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case "ACTIVE":
                return "启用";
            case "INACTIVE":
                return "禁用";
            case "DRAFT":
                return "草稿";
            default:
                return status;
        }
    }
    
    /**
     * 判断是否为新模板（创建时间在24小时内）
     */
    public Boolean isNewTemplate() {
        if (createTime == null) {
            return false;
        }
        
        return createTime.isAfter(LocalDateTime.now().minusDays(1));
    }
    
    /**
     * 判断是否为热门模板（使用次数大于10）
     */
    public Boolean isPopularTemplate() {
        return useCount != null && useCount > 10;
    }
    
    /**
     * 获取最后活动时间（更新时间或最后使用时间中的较晚者）
     */
    public LocalDateTime getLastActivityTime() {
        if (lastUsedTime == null && updateTime == null) {
            return createTime;
        }
        
        if (lastUsedTime == null) {
            return updateTime;
        }
        
        if (updateTime == null) {
            return lastUsedTime;
        }
        
        return lastUsedTime.isAfter(updateTime) ? lastUsedTime : updateTime;
    }
}
