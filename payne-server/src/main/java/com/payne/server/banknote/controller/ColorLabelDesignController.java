package com.payne.server.banknote.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.server.banknote.service.ColorLabelDesignService;
import com.payne.server.banknote.vo.ColorLabelDesignVO;
import com.payne.server.banknote.dto.ColorLabelDesignDto;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 彩签设计控制器
 * 基于现有的批量打印功能扩展
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@RestController
@RequestMapping("/api/color-label-design")
@RequiredArgsConstructor
public class ColorLabelDesignController extends BaseController {

    private final ColorLabelDesignService colorLabelDesignService;

    /**
     * 根据送评单号创建彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/create-by-sendform")
    public ApiResult<?> createBySendform(@RequestBody Map<String, String> params) {
        try {
            String sendformNumber = params.get("sendformNumber");
            String templateId = params.get("templateId");
            
            if (sendformNumber == null || sendformNumber.trim().isEmpty()) {
                return fail("送评单号不能为空");
            }
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return fail("基础模板ID不能为空");
            }
            
            ColorLabelDesignVO result = colorLabelDesignService.createColorLabelBySendform(sendformNumber, templateId);
            return success(result);
        } catch (Exception e) {
            return fail("创建彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 根据送评条码创建彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/create-by-diycode")
    public ApiResult<?> createByDiyCode(@RequestBody Map<String, String> params) {
        try {
            String diyCode = params.get("diyCode");
            String templateId = params.get("templateId");
            
            if (diyCode == null || diyCode.trim().isEmpty()) {
                return fail("送评条码不能为空");
            }
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return fail("基础模板ID不能为空");
            }
            
            ColorLabelDesignVO result = colorLabelDesignService.createColorLabelByDiyCode(diyCode, templateId);
            return success(result);
        } catch (Exception e) {
            return fail("创建彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 根据钱币ID列表创建彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/create-by-coins")
    public ApiResult<?> createByCoins(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            String templateId = (String) params.get("templateId");
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return fail("基础模板ID不能为空");
            }
            
            ColorLabelDesignVO result = colorLabelDesignService.createColorLabelByCoins(coinIds, templateId);
            return success(result);
        } catch (Exception e) {
            return fail("创建彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 保存彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/save")
    public ApiResult<?> saveDesign(@RequestBody ColorLabelDesignDto designDto) {
        try {
            ColorLabelDesignVO result = colorLabelDesignService.saveColorLabelDesign(designDto);
            return success(result);
        } catch (Exception e) {
            return fail("保存彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 获取彩签设计详情
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/{id}")
    public ApiResult<?> getDesign(@PathVariable("id") String id) {
        try {
            ColorLabelDesignVO result = colorLabelDesignService.getColorLabelDesign(id);
            if (result == null) {
                return fail("彩签设计不存在");
            }
            return success(result);
        } catch (Exception e) {
            return fail("获取彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 删除彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:delete')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> deleteDesign(@PathVariable("id") String id) {
        try {
            boolean result = colorLabelDesignService.deleteColorLabelDesign(id);
            if (result) {
                return success("删除成功");
            } else {
                return fail("删除失败");
            }
        } catch (Exception e) {
            return fail("删除彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 预览彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:preview')")
    @OperationLog
    @PostMapping("/preview")
    public ApiResult<?> previewDesign(@RequestBody ColorLabelDesignDto designDto) {
        try {
            Map<String, Object> result = colorLabelDesignService.previewColorLabelDesign(designDto);
            return success(result);
        } catch (Exception e) {
            return fail("预览彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 生成彩签批量打印数据
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:print')")
    @OperationLog
    @PostMapping("/{id}/generate-print-data")
    public ApiResult<?> generatePrintData(@PathVariable("id") String id) {
        try {
            Map<String, Object> result = colorLabelDesignService.generateColorLabelPrintData(id);
            return success(result);
        } catch (Exception e) {
            return fail("生成打印数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取彩签设计历史记录
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/history")
    public ApiResult<?> getDesignHistory(@RequestParam("sendformNumber") String sendformNumber) {
        try {
            List<ColorLabelDesignVO> result = colorLabelDesignService.getColorLabelDesignHistory(sendformNumber);
            return success(result);
        } catch (Exception e) {
            return fail("获取设计历史失败：" + e.getMessage());
        }
    }

    /**
     * 复制彩签设计
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/{id}/copy")
    public ApiResult<?> copyDesign(@PathVariable("id") String id,
                                                   @RequestBody Map<String, String> params) {
        try {
            String newName = params.get("newName");
            if (newName == null || newName.trim().isEmpty()) {
                return fail("新设计名称不能为空");
            }
            
            ColorLabelDesignVO result = colorLabelDesignService.copyColorLabelDesign(id, newName);
            return success(result);
        } catch (Exception e) {
            return fail("复制彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 获取彩签设计模板列表
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/templates")
    public ApiResult<?> getDesignTemplates() {
        try {
            List<ColorLabelDesignVO> result = colorLabelDesignService.getColorLabelDesignTemplates();
            return success(result);
        } catch (Exception e) {
            return fail("获取设计模板失败：" + e.getMessage());
        }
    }

    /**
     * 应用彩签设计模板
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/templates/{templateId}/apply")
    public ApiResult<?> applyDesignTemplate(@PathVariable("templateId") String templateId,
                                                            @RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> coinIds = (List<String>) params.get("coinIds");
            
            if (coinIds == null || coinIds.isEmpty()) {
                return fail("钱币ID列表不能为空");
            }
            
            ColorLabelDesignVO result = colorLabelDesignService.applyColorLabelDesignTemplate(templateId, coinIds);
            return success(result);
        } catch (Exception e) {
            return fail("应用设计模板失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用的彩签元素类型
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:list')")
    @OperationLog
    @GetMapping("/elements")
    public ApiResult<?> getAvailableElements() {
        try {
            List<Map<String, Object>> result = colorLabelDesignService.getAvailableColorLabelElements();
            return success(result);
        } catch (Exception e) {
            return fail("获取彩签元素类型失败：" + e.getMessage());
        }
    }

    /**
     * 验证彩签设计配置
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:design')")
    @OperationLog
    @PostMapping("/validate")
    public ApiResult<?> validateDesign(@RequestBody Map<String, Object> designConfig) {
        try {
            Map<String, Object> result = colorLabelDesignService.validateColorLabelDesign(designConfig);
            return success(result);
        } catch (Exception e) {
            return fail("验证彩签设计失败：" + e.getMessage());
        }
    }

    /**
     * 上传彩签图片素材
     */
    @PreAuthorize("hasAuthority('banknote:colorlabel:upload')")
    @OperationLog
    @PostMapping("/upload-image")
    public ApiResult<?> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return fail("请选择要上传的文件");
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return fail("只能上传图片文件");
            }
            
            // 验证文件大小 (5MB)
            if (file.getSize() > 5 * 1024 * 1024) {
                return fail("图片文件不能超过5MB");
            }
            
            // 这里应该实现实际的文件上传逻辑
            Map<String, Object> result = Map.of(
                "url", "/uploads/color-label/" + file.getOriginalFilename(),
                "fileName", file.getOriginalFilename(),
                "size", file.getSize()
            );
            
            return success(result);
        } catch (Exception e) {
            return fail("上传图片失败：" + e.getMessage());
        }
    }
}
