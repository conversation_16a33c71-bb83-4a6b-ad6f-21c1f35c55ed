package com.payne.server.banknote.param;

import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 彩签模板查询参数
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ColorLabelTemplateParam extends BaseParam {
    
    /**
     * 模板名称（模糊查询）
     */
    @QueryField(type = QueryType.LIKE)
    private String templateName;
    
    /**
     * 模板类型（精确查询）
     */
    @QueryField(type = QueryType.EQ)
    private String templateType;
    
    /**
     * 模板状态（精确查询）
     */
    @QueryField(type = QueryType.EQ)
    private String status;
    
    /**
     * 是否为默认模板
     */
    @QueryField(type = QueryType.EQ)
    private Boolean isDefault;
    
    /**
     * 创建用户（精确查询）
     */
    @QueryField(type = QueryType.EQ)
    private String createUser;
    
    /**
     * 创建时间开始（大于等于）
     */
    @QueryField(type = QueryType.GE, value = "create_time")
    private LocalDateTime startDate;
    
    /**
     * 创建时间结束（小于等于）
     */
    @QueryField(type = QueryType.LE, value = "create_time")
    private LocalDateTime endDate;
    
    /**
     * 更新时间开始（大于等于）
     */
    @QueryField(type = QueryType.GE, value = "update_time")
    private LocalDateTime updateStartDate;
    
    /**
     * 更新时间结束（小于等于）
     */
    @QueryField(type = QueryType.LE, value = "update_time")
    private LocalDateTime updateEndDate;
    
    /**
     * 最后使用时间开始（大于等于）
     */
    @QueryField(type = QueryType.GE, value = "last_used_time")
    private LocalDateTime lastUsedStartDate;
    
    /**
     * 最后使用时间结束（小于等于）
     */
    @QueryField(type = QueryType.LE, value = "last_used_time")
    private LocalDateTime lastUsedEndDate;
    
    /**
     * 使用次数最小值（大于等于）
     */
    @QueryField(type = QueryType.GE, value = "use_count")
    private Integer minUseCount;
    
    /**
     * 使用次数最大值（小于等于）
     */
    @QueryField(type = QueryType.LE, value = "use_count")
    private Integer maxUseCount;
    
    /**
     * 模板描述（模糊查询）
     */
    @QueryField(type = QueryType.LIKE)
    private String description;
    
    /**
     * 扩展字段1（精确查询）
     */
    @QueryField(type = QueryType.EQ)
    private String extField1;
    
    /**
     * 扩展字段2（精确查询）
     */
    @QueryField(type = QueryType.EQ)
    private String extField2;
    
    // 以下字段用于特殊查询条件，不直接映射到数据库字段
    
    /**
     * 关键词搜索（在模板名称和描述中搜索）
     */
    private String keyword;
    
    /**
     * 标签搜索（在扩展字段中搜索标签）
     */
    private String tag;
    
    /**
     * 是否只查询我创建的模板
     */
    private Boolean onlyMine;
    
    /**
     * 是否只查询热门模板（使用次数较多）
     */
    private Boolean onlyPopular;
    
    /**
     * 是否只查询最近创建的模板
     */
    private Boolean onlyRecent;
    
    /**
     * 排序字段：create_time, update_time, use_count, template_name
     */
    private String sortField;
    
    /**
     * 排序方向：asc, desc
     */
    private String sortOrder;
    
    /**
     * 获取默认排序字段
     */
    public String getDefaultSortField() {
        return sortField != null ? sortField : "create_time";
    }
    
    /**
     * 获取默认排序方向
     */
    public String getDefaultSortOrder() {
        return sortOrder != null ? sortOrder : "desc";
    }
    
    /**
     * 是否有时间范围查询条件
     */
    public boolean hasDateRange() {
        return startDate != null || endDate != null;
    }
    
    /**
     * 是否有使用次数范围查询条件
     */
    public boolean hasUseCountRange() {
        return minUseCount != null || maxUseCount != null;
    }
    
    /**
     * 是否有关键词搜索条件
     */
    public boolean hasKeywordSearch() {
        return keyword != null && !keyword.trim().isEmpty();
    }
    
    /**
     * 是否有标签搜索条件
     */
    public boolean hasTagSearch() {
        return tag != null && !tag.trim().isEmpty();
    }
    
    /**
     * 获取处理后的关键词（去除空格并转小写）
     */
    public String getProcessedKeyword() {
        if (keyword == null) {
            return null;
        }
        return keyword.trim().toLowerCase();
    }
    
    /**
     * 获取处理后的标签（去除空格）
     */
    public String getProcessedTag() {
        if (tag == null) {
            return null;
        }
        return tag.trim();
    }
}
