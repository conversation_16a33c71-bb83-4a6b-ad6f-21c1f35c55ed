package com.payne.server.banknote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.server.banknote.entity.ColorLabelDesign;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 彩签设计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface ColorLabelDesignMapper extends BaseMapper<ColorLabelDesign> {

    /**
     * 根据送评单号查询设计列表
     *
     * @param sendformNumber 送评单号
     * @return 设计列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE SENDFORM_NUMBER = #{sendformNumber} " +
            "AND DELETED = 0 ORDER BY CREATE_TIME DESC")
    List<ColorLabelDesign> selectBySendformNumber(@Param("sendformNumber") String sendformNumber);

    /**
     * 根据基础模板ID查询设计列表
     *
     * @param baseTemplateId 基础模板ID
     * @return 设计列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE BASE_TEMPLATE_ID = #{baseTemplateId} " +
            "AND DELETED = 0 ORDER BY CREATE_TIME DESC")
    List<ColorLabelDesign> selectByBaseTemplateId(@Param("baseTemplateId") String baseTemplateId);

    /**
     * 根据设计类型查询设计列表
     *
     * @param designType 设计类型
     * @param status 状态
     * @return 设计列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE DESIGN_TYPE = #{designType} " +
            "AND STATUS = #{status} AND DELETED = 0 ORDER BY CREATE_TIME DESC")
    List<ColorLabelDesign> selectByTypeAndStatus(@Param("designType") String designType, 
                                                @Param("status") String status);

    /**
     * 查询设计模板列表
     *
     * @return 设计模板列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE STATUS = 'TEMPLATE' " +
            "AND DELETED = 0 ORDER BY USE_COUNT DESC, CREATE_TIME DESC")
    List<ColorLabelDesign> selectTemplates();

    /**
     * 更新使用次数
     *
     * @param designId 设计ID
     * @param lastUsedTime 最后使用时间
     * @return 更新行数
     */
    @Update("UPDATE COLOR_LABEL_DESIGN SET USE_COUNT = COALESCE(USE_COUNT, 0) + 1, " +
            "LAST_USED_TIME = #{lastUsedTime} WHERE ID = #{designId} AND DELETED = 0")
    int updateUsageCount(@Param("designId") String designId, 
                        @Param("lastUsedTime") LocalDateTime lastUsedTime);

    /**
     * 根据创建用户查询设计列表
     *
     * @param createUser 创建用户
     * @param limit 限制数量
     * @return 设计列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE CREATE_USER = #{createUser} " +
            "AND DELETED = 0 ORDER BY CREATE_TIME DESC LIMIT #{limit}")
    List<ColorLabelDesign> selectByCreateUser(@Param("createUser") String createUser, 
                                            @Param("limit") Integer limit);

    /**
     * 查询热门设计（使用次数较多）
     *
     * @param minUseCount 最小使用次数
     * @param limit 限制数量
     * @return 设计列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE USE_COUNT >= #{minUseCount} " +
            "AND STATUS != 'DRAFT' AND DELETED = 0 " +
            "ORDER BY USE_COUNT DESC, LAST_USED_TIME DESC LIMIT #{limit}")
    List<ColorLabelDesign> selectPopularDesigns(@Param("minUseCount") Integer minUseCount, 
                                              @Param("limit") Integer limit);

    /**
     * 查询最近创建的设计
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 设计列表
     */
    @Select("SELECT * FROM COLOR_LABEL_DESIGN WHERE CREATE_TIME >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND DELETED = 0 ORDER BY CREATE_TIME DESC LIMIT #{limit}")
    List<ColorLabelDesign> selectRecentDesigns(@Param("days") Integer days, 
                                             @Param("limit") Integer limit);

    /**
     * 统计设计使用情况
     *
     * @param designId 设计ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "d.ID as designId, " +
            "d.DESIGN_NAME as designName, " +
            "d.USE_COUNT as useCount, " +
            "d.LAST_USED_TIME as lastUsedTime, " +
            "d.CREATE_TIME as createTime, " +
            "d.UPDATE_TIME as updateTime " +
            "FROM COLOR_LABEL_DESIGN d " +
            "WHERE d.ID = #{designId} AND d.DELETED = 0")
    Map<String, Object> selectDesignStatistics(@Param("designId") String designId);

    /**
     * 查询设计使用趋势（按月统计）
     *
     * @param designId 设计ID
     * @param months 月数
     * @return 使用趋势数据
     */
    @Select("<script>" +
            "SELECT " +
            "DATE_FORMAT(LAST_USED_TIME, '%Y-%m') as month, " +
            "COUNT(*) as usageCount " +
            "FROM COLOR_LABEL_DESIGN " +
            "WHERE ID = #{designId} " +
            "AND LAST_USED_TIME >= DATE_SUB(NOW(), INTERVAL #{months} MONTH) " +
            "AND DELETED = 0 " +
            "GROUP BY DATE_FORMAT(LAST_USED_TIME, '%Y-%m') " +
            "ORDER BY month DESC" +
            "</script>")
    List<Map<String, Object>> selectUsageTrend(@Param("designId") String designId, 
                                              @Param("months") Integer months);

    /**
     * 批量更新设计状态
     *
     * @param ids 设计ID列表
     * @param status 新状态
     * @param updateUser 更新用户
     * @param updateTime 更新时间
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE COLOR_LABEL_DESIGN SET " +
            "STATUS = #{status}, " +
            "UPDATE_USER = #{updateUser}, " +
            "UPDATE_TIME = #{updateTime} " +
            "WHERE ID IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "AND DELETED = 0" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, 
                         @Param("status") String status,
                         @Param("updateUser") String updateUser, 
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询设计分类统计
     *
     * @return 分类统计信息
     */
    @Select("SELECT " +
            "DESIGN_TYPE as designType, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN STATUS = 'SAVED' THEN 1 ELSE 0 END) as savedCount, " +
            "SUM(CASE WHEN STATUS = 'TEMPLATE' THEN 1 ELSE 0 END) as templateCount, " +
            "SUM(COALESCE(USE_COUNT, 0)) as totalUseCount " +
            "FROM COLOR_LABEL_DESIGN " +
            "WHERE DELETED = 0 " +
            "GROUP BY DESIGN_TYPE " +
            "ORDER BY count DESC")
    List<Map<String, Object>> selectCategoryStatistics();

    /**
     * 检查设计名称是否存在
     *
     * @param designName 设计名称
     * @param excludeId 排除的ID
     * @return 存在的数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM COLOR_LABEL_DESIGN " +
            "WHERE DESIGN_NAME = #{designName} AND DELETED = 0 " +
            "<if test='excludeId != null and excludeId != \"\"'>" +
            "AND ID != #{excludeId}" +
            "</if>" +
            "</script>")
    int countByDesignName(@Param("designName") String designName, 
                         @Param("excludeId") String excludeId);

    /**
     * 查询用户的设计数量
     *
     * @param createUser 创建用户
     * @return 设计数量
     */
    @Select("SELECT COUNT(*) FROM COLOR_LABEL_DESIGN " +
            "WHERE CREATE_USER = #{createUser} AND DELETED = 0")
    int countByCreateUser(@Param("createUser") String createUser);

    /**
     * 清理长期未使用的设计（可选功能）
     *
     * @param days 天数
     * @return 清理的数量
     */
    @Update("UPDATE COLOR_LABEL_DESIGN SET STATUS = 'INACTIVE' " +
            "WHERE (LAST_USED_TIME IS NULL OR LAST_USED_TIME < DATE_SUB(NOW(), INTERVAL #{days} DAY)) " +
            "AND STATUS = 'SAVED' AND DELETED = 0")
    int markInactiveDesigns(@Param("days") Integer days);

    /**
     * 根据关键词搜索设计
     *
     * @param keyword 关键词
     * @param createUser 创建用户（可选）
     * @return 设计列表
     */
    @Select("<script>" +
            "SELECT * FROM COLOR_LABEL_DESIGN WHERE DELETED = 0 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (DESIGN_NAME LIKE CONCAT('%', #{keyword}, '%') " +
            "OR DESCRIPTION LIKE CONCAT('%', #{keyword}, '%') " +
            "OR SENDFORM_NUMBER LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='createUser != null and createUser != \"\"'>" +
            "AND CREATE_USER = #{createUser} " +
            "</if>" +
            "ORDER BY CREATE_TIME DESC" +
            "</script>")
    List<ColorLabelDesign> searchDesigns(@Param("keyword") String keyword, 
                                       @Param("createUser") String createUser);
}
