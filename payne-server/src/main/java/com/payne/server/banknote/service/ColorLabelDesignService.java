package com.payne.server.banknote.service;

import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.vo.ColorLabelDesignVO;
import com.payne.server.banknote.dto.ColorLabelDesignDto;

import java.util.List;
import java.util.Map;

/**
 * 彩签设计服务接口
 * 基于现有的 LabelTemplate 和批量打印功能扩展
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface ColorLabelDesignService {

    /**
     * 根据送评单号获取钱币信息并生成彩签设计数据
     *
     * @param sendformNumber 送评单号
     * @param templateId 基础标签模板ID
     * @return 彩签设计数据
     */
    ColorLabelDesignVO createColorLabelBySendform(String sendformNumber, String templateId);

    /**
     * 根据送评条码获取钱币信息并生成彩签设计数据
     *
     * @param diyCode 送评条码
     * @param templateId 基础标签模板ID
     * @return 彩签设计数据
     */
    ColorLabelDesignVO createColorLabelByDiyCode(String diyCode, String templateId);

    /**
     * 根据钱币ID列表生成彩签设计数据
     *
     * @param coinIds 钱币ID列表
     * @param templateId 基础标签模板ID
     * @return 彩签设计数据
     */
    ColorLabelDesignVO createColorLabelByCoins(List<String> coinIds, String templateId);

    /**
     * 保存彩签设计配置
     *
     * @param designDto 彩签设计数据
     * @return 保存结果
     */
    ColorLabelDesignVO saveColorLabelDesign(ColorLabelDesignDto designDto);

    /**
     * 获取彩签设计详情
     *
     * @param designId 设计ID
     * @return 彩签设计详情
     */
    ColorLabelDesignVO getColorLabelDesign(String designId);

    /**
     * 预览彩签设计效果
     *
     * @param designDto 彩签设计数据
     * @return 预览数据
     */
    Map<String, Object> previewColorLabelDesign(ColorLabelDesignDto designDto);

    /**
     * 生成彩签批量打印数据
     *
     * @param designId 彩签设计ID
     * @return 打印数据
     */
    Map<String, Object> generateColorLabelPrintData(String designId);

    /**
     * 将基础标签模板转换为彩签设计数据
     *
     * @param labelTemplate 基础标签模板
     * @param coinData 钱币数据
     * @return 彩签设计数据
     */
    Map<String, Object> convertLabelTemplateToColorLabel(LabelTemplate labelTemplate, 
                                                        List<PjOSendformItem> coinData);

    /**
     * 合成彩签元素到基础模板
     *
     * @param baseTemplate 基础模板配置
     * @param colorElements 彩签元素配置
     * @param coinData 钱币数据
     * @return 合成后的模板配置
     */
    Map<String, Object> composeColorLabelElements(Map<String, Object> baseTemplate,
                                                 List<Map<String, Object>> colorElements,
                                                 Map<String, Object> coinData);

    /**
     * 获取可用的彩签元素类型
     *
     * @return 元素类型列表
     */
    List<Map<String, Object>> getAvailableColorLabelElements();

    /**
     * 验证彩签设计配置
     *
     * @param designConfig 设计配置
     * @return 验证结果
     */
    Map<String, Object> validateColorLabelDesign(Map<String, Object> designConfig);

    /**
     * 获取彩签设计历史记录
     *
     * @param sendformNumber 送评单号
     * @return 历史记录列表
     */
    List<ColorLabelDesignVO> getColorLabelDesignHistory(String sendformNumber);

    /**
     * 复制彩签设计
     *
     * @param sourceDesignId 源设计ID
     * @param newName 新设计名称
     * @return 复制后的设计
     */
    ColorLabelDesignVO copyColorLabelDesign(String sourceDesignId, String newName);

    /**
     * 删除彩签设计
     *
     * @param designId 设计ID
     * @return 是否删除成功
     */
    boolean deleteColorLabelDesign(String designId);

    /**
     * 获取彩签设计模板列表（用户保存的设计作为模板）
     *
     * @return 设计模板列表
     */
    List<ColorLabelDesignVO> getColorLabelDesignTemplates();

    /**
     * 应用彩签设计模板
     *
     * @param templateDesignId 模板设计ID
     * @param coinIds 钱币ID列表
     * @return 应用后的设计数据
     */
    ColorLabelDesignVO applyColorLabelDesignTemplate(String templateDesignId, List<String> coinIds);
}
