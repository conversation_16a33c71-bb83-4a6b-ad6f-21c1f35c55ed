package com.payne.server.banknote.controller;

import cn.hutool.extra.qrcode.QrCodeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageResult;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.param.BatchPrintParam;
import com.payne.server.banknote.service.BatchPrintService;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.vo.BatchPrintCoinVO;
import com.payne.server.banknote.vo.PrintAuditResultVO;
import com.payne.server.banknote.vo.PrintDataVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * 批量打印控制器
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
@RestController
@RequestMapping("/api/batch-print")
public class  BatchPrintController extends BaseController {

    @Autowired
    private BatchPrintService batchPrintService;

    @Autowired
    private PjOSendformItemService pjOSendformItemService;

    /**
     * 分页查询待打印钱币列表
     */
    @GetMapping("/coins")
    public ApiResult<PageResult<BatchPrintCoinVO>> queryPrintCoins(BatchPrintParam param) {
        // 构建查询条件
        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();

        // 钱币编号查询
        if (param.getCoinNumbers() != null && !param.getCoinNumbers().isEmpty()) {
            wrapper.in(PjOSendformItem::getSerialNumber, param.getCoinNumbers());
        }

        // 送评单号查询
        if (param.getSendformNumbers() != null && !param.getSendformNumbers().isEmpty()) {
            wrapper.in(PjOSendformItem::getSendnum, param.getSendformNumbers());
        }

        // 钱币类型查询
        if (StringUtils.hasText(param.getCoinType())) {
            wrapper.eq(PjOSendformItem::getCoinType, param.getCoinType());
        }

        // 只查询已完成鉴定的钱币（有真伪鉴定结果）
        wrapper.isNotNull(PjOSendformItem::getAuthenticity);

        // 按创建时间倒序
        wrapper.orderByDesc(PjOSendformItem::getSerialNumber);

        IPage<PjOSendformItem> page = new Page<>(param.getCurrent(), param.getSize());
        IPage<PjOSendformItem> result = pjOSendformItemService.page(page, wrapper);

        // 转换为VO
        IPage<BatchPrintCoinVO> voPage = batchPrintService.convertToVOPage(result);

        return success(voPage.getRecords(), voPage.getTotal());
    }

    /**
     * 检查钱币审核状态
     */
    @PostMapping("/check-audit")
    @OperationLog
    public ApiResult<?> checkCoinAuditStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> coinIds = (List<String>) params.get("coinIds");
        
        if (coinIds == null || coinIds.isEmpty()) {
            return fail("钱币ID列表不能为空");
        }

        PrintAuditResultVO result = batchPrintService.checkAuditStatus(coinIds);
        return success(result);
    }

    /**
     * 生成二维码
     */
    @GetMapping("/generatingQRCode")
    public String generatingQRCode(HttpServletResponse response, String codeText) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setContentType("image/png");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode("二维码.png", "UTF-8"));
            QrCodeUtil.generate(codeText, 250, 250, "png",outputStream);
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
    /**
     * 生成打印数据
     */
    @PostMapping("/generate")
    @OperationLog
    public ApiResult<?> generatePrintData(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> coinIds = (List<String>) params.get("coinIds");
        String customTemplateId = (String) params.get("customTemplateId");
        Integer conversionType = parseInteger(params.get("conversionType"));
        String printType = (String) params.get("printType");

        if (coinIds == null || coinIds.isEmpty()) {
            return fail("钱币ID列表不能为空");
        }

        if (customTemplateId == null) {
            return fail("自定义模板ID不能为空");
        }

        PrintDataVO printData = batchPrintService.generateCustomTemplatePrintData(
            coinIds, customTemplateId, conversionType, printType);

        return success(printData);
    }

    /**
     * 获取打印预览数据
     */
    @PostMapping("/preview")
    public ApiResult<?> getPrintPreview(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> coinIds = (List<String>) params.get("coinIds");
        String templateId = (String) params.get("customTemplateId");
        Integer conversionType = parseInteger(params.get("conversionType"));

        if (coinIds == null || coinIds.isEmpty()) {
            return fail("钱币ID列表不能为空");
        }

        if (templateId == null) {
            return fail("自定义模板ID不能为空");
        }

        Map<String, Object> previewData = batchPrintService.generatePreviewData(coinIds, templateId, conversionType);
        return success(previewData);
    }

    /**
     * 安全解析整数值
     *
     * @param value 要解析的对象
     * @return 解析后的整数值，如果无法解析则返回null
     */
    private Integer parseInteger(Object value) {
        if (value == null) {
            return null;
        }

        if (value instanceof Integer) {
            return (Integer) value;
        }

        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        if (value instanceof Number) {
            return ((Number) value).intValue();
        }

        return null;
    }

    /**
     * 获取钱币类型选项
     */
    @GetMapping("/coin-types")
    public ApiResult<?> getCoinTypes() {
        List<Map<String, Object>> coinTypes = Arrays.asList(
            Map.of("label", "纸币", "value", "纸币"),
            Map.of("label", "古钱币", "value", "古钱币"),
            Map.of("label", "机制币", "value", "机制币"),
            Map.of("label", "银锭", "value", "银锭")
        );
        return success(coinTypes);
    }

}
