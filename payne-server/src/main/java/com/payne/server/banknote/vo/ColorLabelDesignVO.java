package com.payne.server.banknote.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 彩签设计视图对象
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class ColorLabelDesignVO {
    
    /**
     * 主键ID
     */
    private String id;
    
    /**
     * 设计名称
     */
    private String designName;
    
    /**
     * 基础标签模板ID
     */
    private String baseTemplateId;
    
    /**
     * 基础标签模板名称
     */
    private String baseTemplateName;
    
    /**
     * 基础标签模板配置（解析后的对象）
     */
    private Map<String, Object> baseTemplateConfig;
    
    /**
     * 送评单号
     */
    private String sendformNumber;
    
    /**
     * 关联的钱币ID列表
     */
    private List<String> coinIds;
    
    /**
     * 钱币数据列表
     */
    private List<Map<String, Object>> coinData;
    
    /**
     * 彩签元素配置（解析后的对象）
     */
    private List<Map<String, Object>> colorElements;
    
    /**
     * 合成后的完整模板配置（解析后的对象）
     */
    private Map<String, Object> composedTemplate;
    
    /**
     * 设计类型
     */
    private String designType;
    
    /**
     * 设计类型显示名称
     */
    private String designTypeName;
    
    /**
     * 设计状态
     */
    private String status;
    
    /**
     * 设计状态显示名称
     */
    private String statusName;
    
    /**
     * 预览图片URL
     */
    private String previewImage;
    
    /**
     * 使用次数
     */
    private Integer useCount;
    
    /**
     * 最后使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastUsedTime;
    
    /**
     * 设计描述
     */
    private String description;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 创建用户名称
     */
    private String createUserName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新用户
     */
    private String updateUser;
    
    /**
     * 更新用户名称
     */
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 钱币数量
     */
    private Integer coinCount;
    
    /**
     * 彩签元素数量
     */
    private Integer elementCount;
    
    /**
     * 是否可编辑
     */
    private Boolean editable;
    
    /**
     * 是否可删除
     */
    private Boolean deletable;
    
    /**
     * 标签信息
     */
    private List<String> tags;
    
    /**
     * 获取设计类型显示名称
     */
    public String getDesignTypeName() {
        if (designType == null) {
            return "未知";
        }
        
        switch (designType) {
            case "SINGLE":
                return "单个钱币";
            case "BATCH":
                return "批量钱币";
            case "TEMPLATE":
                return "设计模板";
            default:
                return designType;
        }
    }
    
    /**
     * 获取状态显示名称
     */
    public String getStatusName() {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case "DRAFT":
                return "草稿";
            case "SAVED":
                return "已保存";
            case "TEMPLATE":
                return "模板";
            default:
                return status;
        }
    }
    
    /**
     * 判断是否为新设计（创建时间在24小时内）
     */
    public Boolean isNewDesign() {
        if (createTime == null) {
            return false;
        }
        
        return createTime.isAfter(LocalDateTime.now().minusDays(1));
    }
    
    /**
     * 判断是否为热门设计（使用次数大于5）
     */
    public Boolean isPopularDesign() {
        return useCount != null && useCount > 5;
    }
    
    /**
     * 获取最后活动时间
     */
    public LocalDateTime getLastActivityTime() {
        if (lastUsedTime == null && updateTime == null) {
            return createTime;
        }
        
        if (lastUsedTime == null) {
            return updateTime;
        }
        
        if (updateTime == null) {
            return lastUsedTime;
        }
        
        return lastUsedTime.isAfter(updateTime) ? lastUsedTime : updateTime;
    }
}
