# 彩签设计功能

## 功能概述

彩签设计功能是基于现有的钱币标签模板（LabelTemplate）和批量打印功能的扩展，允许用户在基础标签模板的基础上添加彩色元素，实现个性化的钱币标签设计和批量打印。

## 核心特性

### 🎯 基于现有模板
- **复用标签模板**：基于现有的 `LabelTemplate` 钱币标签模板
- **数据驱动**：通过送评单号/条码自动获取钱币信息
- **模板填充**：自动将钱币数据填充到基础模板中
- **扩展设计**：在基础模板上添加彩签元素

### 🎨 可视化设计
- **Fabric.js 引擎**：专业级2D图形编辑能力
- **多种元素类型**：文本、图片、二维码、基础形状
- **实时预览**：所见即所得的设计体验
- **图层管理**：基础模板层 + 彩签元素层

### 📊 数据集成
- **送评单集成**：支持送评单号和送评条码查询
- **钱币数据绑定**：自动绑定钱币信息到设计元素
- **批量处理**：支持一个送评单下所有钱币的批量设计

### 🖨️ 批量打印
- **模板合成**：基础模板 + 彩签元素的智能合成
- **打印预览**：批量预览所有钱币的打印效果
- **打印记录**：完整的设计和打印历史记录

## 技术架构

### 前端架构
```
color-label-design/
├── index.vue                    # 主入口页面（步骤导航）
├── components/
│   ├── ColorLabelDesigner.vue   # 彩签设计器
│   ├── ColorLabelPreview.vue    # 预览组件
│   └── CoinSelectorDialog.vue   # 钱币选择器
├── api/
│   └── index.js                 # API接口
└── README.md                    # 文档
```

### 后端架构
```
banknote/
├── controller/
│   └── ColorLabelDesignController.java    # 控制器
├── service/
│   ├── ColorLabelDesignService.java       # 服务接口
│   └── impl/
│       └── ColorLabelDesignServiceImpl.java # 服务实现
├── entity/
│   ├── ColorLabelDesign.java              # 设计实体
│   └── ColorLabelDesignUsage.java         # 使用记录实体
├── vo/
│   └── ColorLabelDesignVO.java            # 视图对象
├── dto/
│   └── ColorLabelDesignDto.java           # 数据传输对象
└── mapper/
    ├── ColorLabelDesignMapper.java        # 数据访问层
    └── ColorLabelDesignUsageMapper.java   # 使用记录访问层
```

## 使用流程

### 1. 数据源选择
```javascript
// 支持三种数据源
const dataSourceTypes = {
  sendform: '送评单号',    // 获取整个送评单的钱币
  diycode: '送评条码',     // 获取单个钱币
  coins: '钱币选择'        // 手动选择钱币
};
```

### 2. 基础模板选择
```javascript
// 从现有的 LabelTemplate 中选择
const labelTemplates = await getLabelTemplates();
```

### 3. 彩签设计
```javascript
// 在基础模板上添加彩签元素
const colorElements = [
  {
    type: 'text',
    text: '{{customerName}}',
    dataBinding: 'customerName',
    style: { color: '#ff0000', fontSize: 14 }
  },
  {
    type: 'qrcode',
    dataBinding: '{{diyCode}}',
    position: { left: 150, top: 5 }
  }
];
```

### 4. 预览和打印
```javascript
// 生成批量打印数据
const printData = await generateColorLabelPrintData(designId);
```

## API接口

### 创建设计
```javascript
// 根据送评单号创建
POST /api/color-label-design/create-by-sendform
{
  "sendformNumber": "SF202501150001",
  "templateId": "TEMPLATE_001"
}

// 根据送评条码创建
POST /api/color-label-design/create-by-diycode
{
  "diyCode": "ZK25080001",
  "templateId": "TEMPLATE_001"
}

// 根据钱币ID列表创建
POST /api/color-label-design/create-by-coins
{
  "coinIds": ["COIN001", "COIN002"],
  "templateId": "TEMPLATE_001"
}
```

### 设计管理
```javascript
// 保存设计
POST /api/color-label-design/save

// 获取设计详情
GET /api/color-label-design/{id}

// 删除设计
DELETE /api/color-label-design/{id}

// 复制设计
POST /api/color-label-design/{id}/copy
```

### 预览和打印
```javascript
// 预览设计
POST /api/color-label-design/preview

// 生成打印数据
POST /api/color-label-design/{id}/generate-print-data
```

## 数据结构

### 彩签设计实体
```sql
CREATE TABLE COLOR_LABEL_DESIGN (
    ID VARCHAR2(50) PRIMARY KEY,
    DESIGN_NAME VARCHAR2(100) NOT NULL,
    BASE_TEMPLATE_ID VARCHAR2(50) NOT NULL,    -- 基础标签模板ID
    SENDFORM_NUMBER VARCHAR2(50),              -- 送评单号
    COIN_IDS CLOB,                            -- 钱币ID列表（JSON）
    COLOR_ELEMENTS CLOB,                      -- 彩签元素配置（JSON）
    COMPOSED_TEMPLATE CLOB,                   -- 合成后的模板（JSON）
    DESIGN_TYPE VARCHAR2(20),                 -- 设计类型
    STATUS VARCHAR2(10),                      -- 状态
    -- 其他字段...
);
```

### 彩签元素结构
```javascript
const colorElement = {
  id: 'element_001',
  type: 'text',                    // 元素类型
  name: '客户名称',                 // 元素名称
  text: '{{customerName}}',        // 文本内容
  dataBinding: 'customerName',     // 数据绑定字段
  position: { left: 20, top: 10 }, // 位置
  style: {                         // 样式
    fontSize: 14,
    fontFamily: 'Microsoft Yahei',
    fill: '#ff0000'
  },
  visible: true,                   // 是否可见
  locked: false                    // 是否锁定
};
```

## 与现有系统的集成

### 1. 标签模板集成
```javascript
// 复用现有的 LabelTemplate
const baseTemplate = await getLabelTemplate(templateId);

// 将标签模板转换为彩签设计基础
const designData = convertLabelTemplateToColorLabel(baseTemplate, coinData);
```

### 2. 批量打印集成
```javascript
// 扩展现有的批量打印功能
const printData = {
  baseTemplate: labelTemplate,     // 基础标签模板
  colorElements: colorElements,    // 彩签元素
  coinData: coinData,             // 钱币数据
  composedTemplate: composedTemplate // 合成后的模板
};
```

### 3. 数据绑定集成
```javascript
// 使用现有的钱币数据字段
const availableFields = [
  { key: 'serialNumber', label: '钱币编号' },
  { key: 'itemName', label: '钱币名称' },
  { key: 'gradeLevel', label: '品相等级' },
  { key: 'customerName', label: '客户名称' },
  { key: 'diyCode', label: '送评条码' }
];
```

## 开发指南

### 前端开发
```javascript
// 1. 初始化设计器
import ColorLabelDesigner from './components/ColorLabelDesigner.vue';

// 2. 传递基础模板和钱币数据
<ColorLabelDesigner
  :base-template="selectedTemplate"
  :coin-data="coinData"
  @save="handleSaveDesign"
  @preview="handlePreviewDesign"
/>

// 3. 处理设计保存
const handleSaveDesign = async (designData) => {
  await saveColorLabelDesign(designData);
};
```

### 后端开发
```java
// 1. 实现服务接口
@Service
public class ColorLabelDesignServiceImpl implements ColorLabelDesignService {
    
    @Override
    public ColorLabelDesignVO createColorLabelBySendform(String sendformNumber, String templateId) {
        // 1. 获取送评单下的钱币数据
        List<PjOSendformItem> coins = getCoinsBySendform(sendformNumber);
        
        // 2. 获取基础标签模板
        LabelTemplate baseTemplate = getLabelTemplate(templateId);
        
        // 3. 转换为彩签设计数据
        return convertToColorLabelDesign(baseTemplate, coins);
    }
}
```

## 部署说明

### 1. 数据库迁移
```sql
-- 执行建表脚本
@V1.0.3__Create_Color_Label_Design.sql
```

### 2. 权限配置
```sql
-- 添加彩签设计权限
INSERT INTO SYS_MENU (MENU_ID, TITLE, AUTHORITY, ...) 
VALUES ('MENU_COLOR_LABEL_DESIGN', '彩签设计', 'banknote:colorlabel:design', ...);
```

### 3. 前端路由
```javascript
// 添加路由配置
{
  path: '/bank-note/color-label-design',
  component: () => import('@/views/bank-note/color-label-design/index.vue'),
  meta: { title: '彩签设计', authority: 'banknote:colorlabel:design' }
}
```

## 注意事项

1. **性能考虑**
   - 大批量钱币设计时建议分批处理
   - 复杂设计可能影响渲染性能
   - 图片素材建议压缩后使用

2. **兼容性**
   - 需要现代浏览器支持（Canvas API）
   - 依赖 Fabric.js 5.3.0+
   - 需要 Vue 3 + Composition API

3. **数据安全**
   - 设计数据包含敏感信息，注意权限控制
   - 上传的图片需要安全验证
   - 打印数据需要审计日志

## 更新日志

### v1.0.0 (2025-01-15)
- ✨ 基于现有标签模板的彩签设计功能
- 🎨 可视化设计器（基于 Fabric.js）
- 📊 送评单数据集成
- 🖨️ 批量打印扩展
- 📱 响应式设计支持
